<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default images first
        $this->createDefaultImages();

        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Premium Real Estate Solutions',
                'type' => 'text',
                'category' => 'general',
                'label' => 'Site Tagline',
                'description' => 'A short description of your website',
                'sort_order' => 2,
            ],
            [
                'key' => 'site_description',
                'value' => 'Hestia Abodes is a premium real estate consultancy based in Pune, committed to helping homebuyers and investors make confident, informed decisions.',
                'type' => 'textarea',
                'category' => 'general',
                'label' => 'Site Description',
                'description' => 'A detailed description of your website',
                'sort_order' => 3,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'category' => 'general',
                'label' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode to show a coming soon page',
                'sort_order' => 4,
            ],

            // Company Information
            [
                'key' => 'company_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'company',
                'label' => 'Company Name',
                'description' => 'Your company name',
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'company_tagline',
                'value' => 'Building Dreams, Creating Homes',
                'type' => 'text',
                'category' => 'company',
                'label' => 'Company Tagline',
                'description' => 'Company tagline or slogan',
                'sort_order' => 2,
            ],
            [
                'key' => 'company_description',
                'value' => 'We are a leading real estate company in Pune dedicated to helping you find your perfect home. With years of experience and deep market knowledge, we provide comprehensive real estate solutions for buyers, sellers, and investors.',
                'type' => 'textarea',
                'category' => 'company',
                'label' => 'Company Description',
                'description' => 'Detailed company description',
                'sort_order' => 3,
            ],

            // Contact Information
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'contact',
                'label' => 'Contact Email',
                'description' => 'Email for contact form submissions',
                'sort_order' => 1,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 98765 43210',
                'type' => 'text',
                'category' => 'contact',
                'label' => 'Contact Phone',
                'description' => 'Phone number for contact inquiries',
                'sort_order' => 2,
            ],
            [
                'key' => 'contact_whatsapp',
                'value' => '+91 98765 43210',
                'type' => 'text',
                'category' => 'contact',
                'label' => 'WhatsApp Number',
                'description' => 'WhatsApp number for quick contact',
                'sort_order' => 3,
            ],
            [
                'key' => 'contact_address',
                'value' => 'Office No. 123, ABC Complex, Pune, Maharashtra 411001, India',
                'type' => 'text',
                'category' => 'contact',
                'label' => 'Contact Address',
                'description' => 'Complete business address',
                'sort_order' => 4,
            ],

            // Social Media
            [
                'key' => 'social_facebook',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Your Facebook page URL',
                'sort_order' => 1,
            ],
            [
                'key' => 'social_twitter',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Your Twitter profile URL',
                'sort_order' => 2,
            ],
            [
                'key' => 'social_instagram',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Your Instagram profile URL',
                'sort_order' => 3,
            ],
            [
                'key' => 'social_linkedin',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'LinkedIn URL',
                'description' => 'Your LinkedIn profile URL',
                'sort_order' => 4,
            ],
            [
                'key' => 'social_whatsapp',
                'value' => '',
                'type' => 'text',
                'category' => 'social',
                'label' => 'WhatsApp Number',
                'description' => 'WhatsApp number with country code',
                'sort_order' => 5,
            ],

            // SEO Settings
            [
                'key' => 'seo_meta_title',
                'value' => 'Hestia Abodes - Premium Real Estate Solutions',
                'type' => 'text',
                'category' => 'seo',
                'label' => 'Meta Title',
                'description' => 'Default meta title for pages',
                'sort_order' => 1,
            ],
            [
                'key' => 'seo_meta_description',
                'value' => 'Premium real estate consultancy in Pune. Expert guidance for homebuyers and investors.',
                'type' => 'textarea',
                'category' => 'seo',
                'label' => 'Meta Description',
                'description' => 'Default meta description for pages',
                'sort_order' => 2,
            ],
            [
                'key' => 'seo_google_analytics',
                'value' => '',
                'type' => 'text',
                'category' => 'seo',
                'label' => 'Google Analytics ID',
                'description' => 'Google Analytics tracking ID (e.g., GA-XXXXXXXXX)',
                'sort_order' => 3,
            ],

            // Email Settings
            [
                'key' => 'email_from_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'email',
                'label' => 'From Name',
                'description' => 'Name used in outgoing emails',
                'sort_order' => 1,
            ],
            [
                'key' => 'email_from_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'email',
                'label' => 'From Email',
                'description' => 'Email address used in outgoing emails',
                'sort_order' => 2,
            ],

            // Appearance Settings
            [
                'key' => 'logo',
                'value' => 'logos/default-logo.svg',
                'type' => 'file',
                'category' => 'appearance',
                'label' => 'Company Logo',
                'description' => 'Upload your company logo (PNG, JPG, SVG recommended)',
                'sort_order' => 1,
            ],
            [
                'key' => 'favicon',
                'value' => 'favicons/default-favicon.svg',
                'type' => 'file',
                'category' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Upload favicon (16x16 or 32x32 pixels, ICO or PNG)',
                'sort_order' => 2,
            ],
            [
                'key' => 'primary_color',
                'value' => '#f8c146',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Primary Color',
                'description' => 'Main brand color used for buttons and highlights',
                'sort_order' => 3,
            ],
            [
                'key' => 'secondary_color',
                'value' => '#8b751d',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Secondary Color',
                'description' => 'Secondary brand color for accents',
                'sort_order' => 4,
            ],
            [
                'key' => 'accent_color',
                'value' => '#007bff',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Accent Color',
                'description' => 'Accent color for links and interactive elements',
                'sort_order' => 5,
            ],
            [
                'key' => 'text_color',
                'value' => '#333333',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Text Color',
                'description' => 'Primary text color',
                'sort_order' => 6,
            ],
            [
                'key' => 'footer_text',
                'value' => '© 2024 Hestia Abodes. All rights reserved.',
                'type' => 'text',
                'category' => 'appearance',
                'label' => 'Footer Text',
                'description' => 'Copyright text in footer',
                'sort_order' => 7,
            ],

            // Features
            [
                'key' => 'feature_enable_testimonials',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'features',
                'label' => 'Enable Testimonials',
                'description' => 'Show testimonials section on website',
                'sort_order' => 1,
            ],
            [
                'key' => 'feature_enable_contact_form',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'features',
                'label' => 'Enable Contact Form',
                'description' => 'Allow visitors to submit contact forms',
                'sort_order' => 2,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Create default logo and favicon placeholders
     */
    private function createDefaultImages()
    {
        // Ensure storage directories exist
        Storage::disk('public')->makeDirectory('logos');
        Storage::disk('public')->makeDirectory('favicons');

        // Create a simple SVG logo placeholder
        $logoSvg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
    <rect width="200" height="60" fill="#f8c146" rx="8"/>
    <text x="100" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#333333">
        Hestia Abodes
    </text>
</svg>';

        // Create a simple favicon SVG
        $faviconSvg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" fill="#f8c146" rx="4"/>
    <text x="16" y="22" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333333">
        H
    </text>
</svg>';

        // Save logo if it doesn't exist
        $logoPath = 'logos/default-logo.svg';
        if (!Storage::disk('public')->exists($logoPath)) {
            Storage::disk('public')->put($logoPath, $logoSvg);
        }

        // Save favicon if it doesn't exist
        $faviconPath = 'favicons/default-favicon.svg';
        if (!Storage::disk('public')->exists($faviconPath)) {
            Storage::disk('public')->put($faviconPath, $faviconSvg);
        }
    }
}
