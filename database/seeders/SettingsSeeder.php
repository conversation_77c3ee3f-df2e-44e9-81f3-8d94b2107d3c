<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Premium Real Estate Solutions',
                'type' => 'text',
                'category' => 'general',
                'label' => 'Site Tagline',
                'description' => 'A short description of your website',
                'sort_order' => 2,
            ],
            [
                'key' => 'site_description',
                'value' => 'Hestia Abodes is a premium real estate consultancy based in Pune, committed to helping homebuyers and investors make confident, informed decisions.',
                'type' => 'textarea',
                'category' => 'general',
                'label' => 'Site Description',
                'description' => 'A detailed description of your website',
                'sort_order' => 3,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'category' => 'general',
                'label' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode to show a coming soon page',
                'sort_order' => 4,
            ],

            // Company Information
            [
                'key' => 'company_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'company',
                'label' => 'Company Name',
                'description' => 'Your company name',
                'is_required' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'company_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'company',
                'label' => 'Company Email',
                'description' => 'Primary company email address',
                'sort_order' => 2,
            ],
            [
                'key' => 'company_phone',
                'value' => '+91 98765 43210',
                'type' => 'text',
                'category' => 'company',
                'label' => 'Company Phone',
                'description' => 'Primary company phone number',
                'sort_order' => 3,
            ],
            [
                'key' => 'company_address',
                'value' => 'Pune, Maharashtra, India',
                'type' => 'textarea',
                'category' => 'company',
                'label' => 'Company Address',
                'description' => 'Complete company address',
                'sort_order' => 4,
            ],

            // Contact Information
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'contact',
                'label' => 'Contact Email',
                'description' => 'Email for contact form submissions',
                'sort_order' => 1,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 98765 43210',
                'type' => 'text',
                'category' => 'contact',
                'label' => 'Contact Phone',
                'description' => 'Phone number for contact inquiries',
                'sort_order' => 2,
            ],
            [
                'key' => 'working_hours',
                'value' => 'Monday - Saturday: 9:00 AM - 6:00 PM',
                'type' => 'text',
                'category' => 'contact',
                'label' => 'Working Hours',
                'description' => 'Business working hours',
                'sort_order' => 3,
            ],

            // Social Media
            [
                'key' => 'social_facebook',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Your Facebook page URL',
                'sort_order' => 1,
            ],
            [
                'key' => 'social_twitter',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Your Twitter profile URL',
                'sort_order' => 2,
            ],
            [
                'key' => 'social_instagram',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Your Instagram profile URL',
                'sort_order' => 3,
            ],
            [
                'key' => 'social_linkedin',
                'value' => '',
                'type' => 'url',
                'category' => 'social',
                'label' => 'LinkedIn URL',
                'description' => 'Your LinkedIn profile URL',
                'sort_order' => 4,
            ],
            [
                'key' => 'social_whatsapp',
                'value' => '',
                'type' => 'text',
                'category' => 'social',
                'label' => 'WhatsApp Number',
                'description' => 'WhatsApp number with country code',
                'sort_order' => 5,
            ],

            // SEO Settings
            [
                'key' => 'seo_meta_title',
                'value' => 'Hestia Abodes - Premium Real Estate Solutions',
                'type' => 'text',
                'category' => 'seo',
                'label' => 'Meta Title',
                'description' => 'Default meta title for pages',
                'sort_order' => 1,
            ],
            [
                'key' => 'seo_meta_description',
                'value' => 'Premium real estate consultancy in Pune. Expert guidance for homebuyers and investors.',
                'type' => 'textarea',
                'category' => 'seo',
                'label' => 'Meta Description',
                'description' => 'Default meta description for pages',
                'sort_order' => 2,
            ],
            [
                'key' => 'seo_google_analytics',
                'value' => '',
                'type' => 'text',
                'category' => 'seo',
                'label' => 'Google Analytics ID',
                'description' => 'Google Analytics tracking ID (e.g., GA-XXXXXXXXX)',
                'sort_order' => 3,
            ],

            // Email Settings
            [
                'key' => 'email_from_name',
                'value' => 'Hestia Abodes',
                'type' => 'text',
                'category' => 'email',
                'label' => 'From Name',
                'description' => 'Name used in outgoing emails',
                'sort_order' => 1,
            ],
            [
                'key' => 'email_from_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'email',
                'label' => 'From Email',
                'description' => 'Email address used in outgoing emails',
                'sort_order' => 2,
            ],

            // Appearance Settings
            [
                'key' => 'appearance_logo',
                'value' => '',
                'type' => 'file',
                'category' => 'appearance',
                'label' => 'Company Logo',
                'description' => 'Upload your company logo (PNG, JPG, SVG recommended)',
                'sort_order' => 1,
            ],
            [
                'key' => 'appearance_favicon',
                'value' => '',
                'type' => 'file',
                'category' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Upload favicon (16x16 or 32x32 pixels, ICO or PNG)',
                'sort_order' => 2,
            ],
            [
                'key' => 'appearance_primary_color',
                'value' => '#f8c146',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Primary Color',
                'description' => 'Main brand color used for buttons and highlights',
                'sort_order' => 3,
            ],
            [
                'key' => 'appearance_secondary_color',
                'value' => '#8b751d',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Secondary Color',
                'description' => 'Secondary brand color for accents',
                'sort_order' => 4,
            ],
            [
                'key' => 'appearance_accent_color',
                'value' => '#007bff',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Accent Color',
                'description' => 'Accent color for links and interactive elements',
                'sort_order' => 5,
            ],
            [
                'key' => 'appearance_background_color',
                'value' => '#ffffff',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Background Color',
                'description' => 'Main background color for the website',
                'sort_order' => 6,
            ],
            [
                'key' => 'appearance_text_color',
                'value' => '#333333',
                'type' => 'color',
                'category' => 'appearance',
                'label' => 'Text Color',
                'description' => 'Primary text color',
                'sort_order' => 7,
            ],
            [
                'key' => 'appearance_footer_text',
                'value' => '© 2024 Hestia Abodes. All rights reserved.',
                'type' => 'text',
                'category' => 'appearance',
                'label' => 'Footer Text',
                'description' => 'Copyright text in footer',
                'sort_order' => 8,
            ],

            // Features
            [
                'key' => 'feature_enable_testimonials',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'features',
                'label' => 'Enable Testimonials',
                'description' => 'Show testimonials section on website',
                'sort_order' => 1,
            ],
            [
                'key' => 'feature_enable_contact_form',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'features',
                'label' => 'Enable Contact Form',
                'description' => 'Allow visitors to submit contact forms',
                'sort_order' => 2,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
