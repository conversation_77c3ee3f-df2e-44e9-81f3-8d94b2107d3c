<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\PageSection;

class WebsiteContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create pages first
        $this->createPages();

        // Create home page sections
        $this->createHomePageSections();

        // Create about page sections
        $this->createAboutPageSections();

        // Create projects page sections
        $this->createProjectsPageSections();
    }

    private function createPages()
    {
        $pages = [
            [
                'name' => 'Home',
                'slug' => 'home',
                'title' => 'Hestia Abodes - Premium Real Estate Solutions',
                'content' => 'Welcome to Hestia Abodes - Premium Real Estate Solutions',
                'meta_description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'About Us',
                'slug' => 'about-us',
                'title' => 'About Hestia Abodes - Your Trusted Real Estate Partner',
                'content' => 'Your Real Estate Journey, Simplified with Strategy and Trust',
                'meta_description' => 'Hestia Abodes is a premier real estate company dedicated to providing exceptional residential and commercial properties with years of experience in the industry.',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Our Projects',
                'slug' => 'projects',
                'title' => 'Our Projects - Hestia Abodes',
                'content' => 'Discover Premium Properties Across Prime Locations',
                'meta_description' => 'Explore our curated selection of premium residential and commercial properties across prime locations.',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Services',
                'slug' => 'services',
                'title' => 'Our Services - Hestia Abodes',
                'content' => 'Comprehensive Real Estate Services',
                'meta_description' => 'From property advisory to legal documentation, we provide end-to-end real estate services.',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Contact Us',
                'slug' => 'contact',
                'title' => 'Contact Us - Hestia Abodes',
                'content' => 'Get in Touch with Our Real Estate Experts',
                'meta_description' => 'Contact Hestia Abodes for all your real estate needs. We\'re here to help you find your perfect property.',
                'is_active' => true,
                'sort_order' => 5
            ]
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }
    }

    private function createHomePageSections()
    {
        $homePage = Page::where('slug', 'home')->first();
        if (!$homePage) return;

        $sections = [
            [
                'section_type' => 'hero',
                'title' => 'Hestia Abodes',
                'subtitle' => null,
                'content' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
                'description' => null,
                'images' => ['/images/pages/home/<USER>'],
                'cta_text' => 'Explore Projects',
                'cta_url' => '/projects',
                'secondary_cta_text' => 'Get in Touch',
                'secondary_cta_url' => '/contact',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'section_type' => 'about',
                'title' => 'Who We Are',
                'subtitle' => null,
                'content' => 'Hestia Abodes is a premium real estate consultancy firm based in Pune, committed to helping homebuyers and investors make confident, informed decisions in an ever-evolving property market.<br><br>As trusted Channel Partners (CPs), we bridge the gap between buyers and top-tier developers by offering personalized, transparent, and end-to-end real estate advisory services.<br><br>Founded on the principles of <strong>integrity, insight, and impact</strong>, we go beyond listings. We take the time to understand not just the market—but also the mindset of our clients.<br><br>Whether you\'re a first-time buyer, luxury home seeker, or seasoned investor, we provide tailored solutions that align with both lifestyle and financial goals.',
                'description' => null,
                'images' => ['/images/pages/home/<USER>'],
                'show_stats' => true,
                'stats' => [
                    ['number' => '5+', 'label' => 'Years of Excellence']
                ],
                'items' => [
                    ['title' => 'Integrity', 'description' => null],
                    ['title' => 'Insight', 'description' => null],
                    ['title' => 'Impact', 'description' => null]
                ],
                'sort_order' => 2,
                'is_active' => true
            ]
        ];

        foreach ($sections as $sectionData) {
            $sectionData['page_id'] = $homePage->id;
            PageSection::updateOrCreate(
                [
                    'page_id' => $homePage->id,
                    'section_type' => $sectionData['section_type'],
                    'sort_order' => $sectionData['sort_order']
                ],
                $sectionData
            );
        }
    }

    private function createAboutPageSections()
    {
        $aboutPage = Page::where('slug', 'about-us')->first();
        if (!$aboutPage) return;

        $sections = [
            [
                'section_type' => 'hero',
                'title' => 'About Us',
                'subtitle' => 'Your Real Estate Journey, Simplified with Strategy and Trust',
                'content' => null,
                'description' => null,
                'images' => ['/images/pages/about/hero-bg.jpg'],
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'section_type' => 'about',
                'title' => 'Who We Are',
                'subtitle' => 'About Hestia Abodes',
                'content' => 'Hestia Abodes is a premier real estate company dedicated to providing exceptional residential and commercial properties. With years of experience in the industry, we have established ourselves as a trusted partner for property buyers, sellers, and investors.',
                'description' => null,
                'images' => ['/images/pages/about/about-image.jpg'],
                'show_stats' => true,
                'stats' => [
                    ['number' => '5+', 'label' => 'Years of Excellence']
                ],
                'items' => [
                    ['title' => 'Integrity', 'description' => null],
                    ['title' => 'Insight', 'description' => null],
                    ['title' => 'Impact', 'description' => null]
                ],
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'section_type' => 'mission_vision',
                'title' => 'Our Mission & Vision',
                'subtitle' => null,
                'content' => null,
                'description' => null,
                'items' => [
                    [
                        'title' => 'Our Mission',
                        'description' => 'To provide world-class real estate solutions that exceed our clients\' expectations while maintaining the highest standards of integrity, professionalism, and customer service.'
                    ],
                    [
                        'title' => 'Our Vision',
                        'description' => 'To be the most trusted and preferred real estate brand, known for innovation, quality, and customer satisfaction.'
                    ]
                ],
                'sort_order' => 3,
                'is_active' => true
            ]
        ];

        foreach ($sections as $sectionData) {
            $sectionData['page_id'] = $aboutPage->id;
            PageSection::updateOrCreate(
                [
                    'page_id' => $aboutPage->id,
                    'section_type' => $sectionData['section_type'],
                    'sort_order' => $sectionData['sort_order']
                ],
                $sectionData
            );
        }
    }

    private function createProjectsPageSections()
    {
        $projectsPage = Page::where('slug', 'projects')->first();
        if (!$projectsPage) return;

        $sections = [
            [
                'section_type' => 'hero',
                'title' => 'Our Projects',
                'subtitle' => 'Discover Premium Properties Across Prime Locations',
                'content' => null,
                'description' => null,
                'images' => ['/images/pages/projects/hero-bg.jpg'],
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'section_type' => 'project_filters',
                'title' => 'Filter Projects',
                'subtitle' => null,
                'content' => null,
                'description' => null,
                'items' => [
                    ['title' => 'All Projects', 'filter' => 'all'],
                    ['title' => 'Residential', 'filter' => 'residential'],
                    ['title' => 'Commercial', 'filter' => 'commercial'],
                    ['title' => 'Featured', 'filter' => 'featured'],
                    ['title' => 'Ready to Move', 'filter' => 'ready'],
                    ['title' => 'Upcoming', 'filter' => 'upcoming']
                ],
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'section_type' => 'cta',
                'title' => 'Can\'t Find What You\'re Looking For?',
                'subtitle' => null,
                'content' => 'Let our experts help you find the perfect property that matches your requirements and budget.',
                'description' => null,
                'cta_text' => 'Get Personalized Recommendations',
                'cta_url' => '/contact',
                'secondary_cta_text' => 'Call Now: +91 ************',
                'secondary_cta_url' => 'tel:+919067881848',
                'sort_order' => 3,
                'is_active' => true
            ]
        ];

        foreach ($sections as $sectionData) {
            $sectionData['page_id'] = $projectsPage->id;
            PageSection::updateOrCreate(
                [
                    'page_id' => $projectsPage->id,
                    'section_type' => $sectionData['section_type'],
                    'sort_order' => $sectionData['sort_order']
                ],
                $sectionData
            );
        }
    }
}
