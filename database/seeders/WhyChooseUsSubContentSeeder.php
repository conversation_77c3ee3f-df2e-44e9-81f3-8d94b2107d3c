<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeContent;

class WhyChooseUsSubContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find the Why Choose Us main content
        $whyChooseUs = HomeContent::where('section', 'why_choose_us')
            ->whereNull('parent_id')
            ->first();

        if (!$whyChooseUs) {
            $this->command->error('Why Choose Us main content not found!');
            return;
        }

        // Delete existing sub-content for this section
        HomeContent::where('parent_id', $whyChooseUs->id)->delete();

        // Why Choose Us Sub-Content Items
        $whyChooseUsItems = [
            [
                'title' => 'Selective, Not Generic',
                'description' => 'We only recommend what fits you. Our curated approach ensures you see properties that truly match your needs and preferences.',
                'sort_order' => 1
            ],
            [
                'title' => 'We Represent You, Not the Developer',
                'description' => 'Your goals come first. As independent consultants, we provide unbiased advice focused on your best interests.',
                'sort_order' => 2
            ],
            [
                'title' => 'Market Insight Over Marketing',
                'description' => 'We share context, not just content. Get real market analysis and honest feedback, not sales pitches.',
                'sort_order' => 3
            ],
            [
                'title' => 'Process-Driven',
                'description' => 'You always know what\'s happening next. Our systematic approach ensures transparency at every step of your journey.',
                'sort_order' => 4
            ],
            [
                'title' => 'Compliance-First',
                'description' => 'We protect your investment with legal clarity. All documentation and RERA compliance thoroughly verified.',
                'sort_order' => 5
            ],
            [
                'title' => 'Here for the Long Haul',
                'description' => 'Even after deal closure, we remain your advisors. Long-term relationships matter more than one-time transactions.',
                'sort_order' => 6
            ]
        ];

        foreach ($whyChooseUsItems as $item) {
            HomeContent::create([
                'section' => 'why_choose_us',
                'parent_id' => $whyChooseUs->id,
                'title' => $item['title'],
                'description' => $item['description'],
                'sort_order' => $item['sort_order'],
                'is_active' => true,
                'item_type' => 'sub_item'
            ]);
        }

        $this->command->info('Why Choose Us sub-content created successfully!');
    }
}
