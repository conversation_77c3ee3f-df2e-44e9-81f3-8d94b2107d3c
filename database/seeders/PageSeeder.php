<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'name' => 'About Us',
                'slug' => 'about-us',
                'title' => 'About Hestia Abodes - Your Trusted Real Estate Partner',
                'meta_description' => 'Learn about Hestia Abodes, a leading real estate company providing premium residential and commercial properties with exceptional service.',
                'meta_keywords' => 'about hestia abodes, real estate company, property developers, residential commercial properties',
                'content' => '<div class="about-content">
                    <h2>About Hestia Abodes</h2>
                    <p>Hestia Abodes is a premier real estate company dedicated to providing exceptional residential and commercial properties. With years of experience in the industry, we have established ourselves as a trusted partner for property buyers, sellers, and investors.</p>

                    <h3>Our Mission</h3>
                    <p>To provide world-class real estate solutions that exceed our clients\' expectations while maintaining the highest standards of integrity, professionalism, and customer service.</p>

                    <h3>Our Vision</h3>
                    <p>To be the most trusted and preferred real estate brand, known for innovation, quality, and customer satisfaction.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'hero_section',
                        'title' => 'About Hestia Abodes',
                        'subtitle' => 'Your Trusted Real Estate Partner',
                        'description' => 'Discover our journey, values, and commitment to excellence in real estate.',
                        'image' => 'images/about-hero.jpg',
                        'button_text' => 'Learn More',
                        'button_link' => '#about-content'
                    ],
                    [
                        'type' => 'stats',
                        'key' => 'company_stats',
                        'title' => 'Our Achievements',
                        'stats' => [
                            ['number' => '500+', 'label' => 'Happy Clients'],
                            ['number' => '100+', 'label' => 'Projects Completed'],
                            ['number' => '15+', 'label' => 'Years Experience'],
                            ['number' => '50+', 'label' => 'Expert Team']
                        ]
                    ],
                    [
                        'type' => 'team',
                        'key' => 'our_team',
                        'title' => 'Meet Our Team',
                        'description' => 'Our experienced professionals are dedicated to helping you find your perfect property.',
                        'members' => [
                            [
                                'name' => 'John Doe',
                                'position' => 'CEO & Founder',
                                'image' => 'images/team/john-doe.jpg',
                                'bio' => 'With over 20 years in real estate, John leads our vision.'
                            ],
                            [
                                'name' => 'Jane Smith',
                                'position' => 'Head of Sales',
                                'image' => 'images/team/jane-smith.jpg',
                                'bio' => 'Jane brings 15 years of sales expertise to our team.'
                            ]
                        ]
                    ]
                ],
                'template' => 'about',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'Contact Us',
                'slug' => 'contact',
                'title' => 'Contact Hestia Abodes - Get in Touch Today',
                'meta_description' => 'Contact Hestia Abodes for all your real estate needs. Visit our office, call us, or send us a message. We\'re here to help you find your dream property.',
                'meta_keywords' => 'contact hestia abodes, real estate contact, property inquiry, office location, phone number',
                'content' => '<div class="contact-content">
                    <h2>Get in Touch</h2>
                    <p>Ready to find your dream property? Our expert team is here to help you every step of the way. Contact us today to discuss your real estate needs.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'contact_info',
                        'key' => 'contact_details',
                        'title' => 'Contact Information',
                        'phone' => '+91 9876543210',
                        'email' => '<EMAIL>',
                        'address' => '123 Business District, Gurgaon, Haryana 122001',
                        'working_hours' => 'Mon - Sat: 9:00 AM - 7:00 PM'
                    ],
                    [
                        'type' => 'contact_form',
                        'key' => 'inquiry_form',
                        'title' => 'Send us a Message',
                        'description' => 'Fill out the form below and we\'ll get back to you within 24 hours.',
                        'fields' => [
                            ['name' => 'name', 'type' => 'text', 'label' => 'Full Name', 'required' => true],
                            ['name' => 'email', 'type' => 'email', 'label' => 'Email Address', 'required' => true],
                            ['name' => 'phone', 'type' => 'tel', 'label' => 'Phone Number', 'required' => true],
                            ['name' => 'subject', 'type' => 'text', 'label' => 'Subject', 'required' => false],
                            ['name' => 'message', 'type' => 'textarea', 'label' => 'Message', 'required' => true]
                        ]
                    ]
                ],
                'template' => 'contact',
                'is_active' => true,
                'sort_order' => 2
            ]
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }
}
