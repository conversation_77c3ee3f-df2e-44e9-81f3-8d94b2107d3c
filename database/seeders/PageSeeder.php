<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'name' => 'Home',
                'slug' => 'home',
                'title' => 'Hestia Abodes - Premium Real Estate Solutions',
                'meta_description' => 'Discover premium real estate opportunities with Hestia Abodes. Expert property consulting, investment guidance, and personalized service for buyers and builders.',
                'meta_keywords' => 'real estate, property, investment, consulting, premium properties, Hestia Abodes',
                'content' => '<p>Welcome to Hestia Abodes - Your trusted partner in real estate excellence.</p>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'hero_section',
                        'title' => 'Hestia Abodes',
                        'subtitle' => 'Premium Real Estate Solutions',
                        'description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
                        'image' => 'images/pages/home/<USER>',
                        'button_text' => 'Explore Projects',
                        'button_link' => '/projects'
                    ],
                    [
                        'type' => 'stats',
                        'key' => 'company_stats',
                        'title' => 'Our Achievements',
                        'stats' => [
                            ['number' => '500+', 'label' => 'Happy Clients'],
                            ['number' => '100+', 'label' => 'Projects Completed'],
                            ['number' => '15+', 'label' => 'Years Experience'],
                            ['number' => '50+', 'label' => 'Expert Team']
                        ]
                    ]
                ],
                'template' => 'default',
                'is_active' => true,
                'sort_order' => 0,
                'images' => [
                    'images/pages/home/<USER>',
                    'images/pages/home/<USER>',
                    'images/pages/home/<USER>',
                    'images/pages/home/<USER>'
                ]
            ],
            [
                'name' => 'About Us',
                'slug' => 'about-us',
                'title' => 'About Hestia Abodes - Your Trusted Real Estate Partner',
                'meta_description' => 'Learn about Hestia Abodes, a leading real estate company providing premium residential and commercial properties with exceptional service.',
                'meta_keywords' => 'about hestia abodes, real estate company, property developers, residential commercial properties',
                'content' => '<div class="about-content">
                    <h2>About Hestia Abodes</h2>
                    <p>Hestia Abodes is a premier real estate company dedicated to providing exceptional residential and commercial properties. With years of experience in the industry, we have established ourselves as a trusted partner for property buyers, sellers, and investors.</p>

                    <h3>Our Mission</h3>
                    <p>To provide world-class real estate solutions that exceed our clients\' expectations while maintaining the highest standards of integrity, professionalism, and customer service.</p>

                    <h3>Our Vision</h3>
                    <p>To be the most trusted and preferred real estate brand, known for innovation, quality, and customer satisfaction.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'hero_section',
                        'title' => 'About Hestia Abodes',
                        'subtitle' => 'Your Trusted Real Estate Partner',
                        'description' => 'Discover our journey, values, and commitment to excellence in real estate.',
                        'image' => 'images/about-hero.jpg',
                        'button_text' => 'Learn More',
                        'button_link' => '#about-content'
                    ],
                    [
                        'type' => 'stats',
                        'key' => 'company_stats',
                        'title' => 'Our Achievements',
                        'stats' => [
                            ['number' => '500+', 'label' => 'Happy Clients'],
                            ['number' => '100+', 'label' => 'Projects Completed'],
                            ['number' => '15+', 'label' => 'Years Experience'],
                            ['number' => '50+', 'label' => 'Expert Team']
                        ]
                    ],
                    [
                        'type' => 'team',
                        'key' => 'our_team',
                        'title' => 'Meet Our Team',
                        'description' => 'Our experienced professionals are dedicated to helping you find your perfect property.',
                        'members' => [
                            [
                                'name' => 'John Doe',
                                'position' => 'CEO & Founder',
                                'image' => 'images/team/john-doe.jpg',
                                'bio' => 'With over 20 years in real estate, John leads our vision.'
                            ],
                            [
                                'name' => 'Jane Smith',
                                'position' => 'Head of Sales',
                                'image' => 'images/team/jane-smith.jpg',
                                'bio' => 'Jane brings 15 years of sales expertise to our team.'
                            ]
                        ]
                    ]
                ],
                'template' => 'about',
                'is_active' => true,
                'sort_order' => 1,
                'images' => [
                    'images/pages/about/hero-bg.jpg',
                    'images/pages/about/about-image.jpg',
                    'images/pages/about/default-avatar.jpg'
                ]
            ],
            [
                'name' => 'Contact Us',
                'slug' => 'contact',
                'title' => 'Contact Hestia Abodes - Get in Touch Today',
                'meta_description' => 'Contact Hestia Abodes for all your real estate needs. Visit our office, call us, or send us a message. We\'re here to help you find your dream property.',
                'meta_keywords' => 'contact hestia abodes, real estate contact, property inquiry, office location, phone number',
                'content' => '<div class="contact-content">
                    <h2>Get in Touch</h2>
                    <p>Ready to find your dream property? Our expert team is here to help you every step of the way. Contact us today to discuss your real estate needs.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'contact_info',
                        'key' => 'contact_details',
                        'title' => 'Contact Information',
                        'phone' => '+91 9876543210',
                        'email' => '<EMAIL>',
                        'address' => '123 Business District, Gurgaon, Haryana 122001',
                        'working_hours' => 'Mon - Sat: 9:00 AM - 7:00 PM'
                    ],
                    [
                        'type' => 'contact_form',
                        'key' => 'inquiry_form',
                        'title' => 'Send us a Message',
                        'description' => 'Fill out the form below and we\'ll get back to you within 24 hours.',
                        'fields' => [
                            ['name' => 'name', 'type' => 'text', 'label' => 'Full Name', 'required' => true],
                            ['name' => 'email', 'type' => 'email', 'label' => 'Email Address', 'required' => true],
                            ['name' => 'phone', 'type' => 'tel', 'label' => 'Phone Number', 'required' => true],
                            ['name' => 'subject', 'type' => 'text', 'label' => 'Subject', 'required' => false],
                            ['name' => 'message', 'type' => 'textarea', 'label' => 'Message', 'required' => true]
                        ]
                    ]
                ],
                'template' => 'contact',
                'is_active' => true,
                'sort_order' => 2,
                'images' => [
                    'images/pages/contact/hero-bg.jpg',
                    'images/pages/contact/contact-image.jpg'
                ]
            ],
            [
                'name' => 'Our Services',
                'slug' => 'services',
                'title' => 'Our Services - Hestia Abodes Real Estate Solutions',
                'meta_description' => 'Discover comprehensive real estate services by Hestia Abodes including property buying, selling, investment consultation, and property management.',
                'meta_keywords' => 'real estate services, property buying, property selling, investment consultation, property management',
                'content' => '<div class="services-content">
                    <h2>Our Services</h2>
                    <p>We offer comprehensive real estate solutions tailored to meet your specific needs. From property buying to investment consultation, our expert team is here to guide you every step of the way.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'services_hero',
                        'title' => 'Our Services',
                        'subtitle' => 'Comprehensive Real Estate Solutions',
                        'description' => 'From property buying to investment consultation, we offer end-to-end real estate services.',
                        'image' => 'images/services-hero.jpg'
                    ],
                    [
                        'type' => 'services_grid',
                        'key' => 'services_list',
                        'title' => 'What We Offer',
                        'services' => [
                            [
                                'title' => 'Property Buying',
                                'description' => 'Expert guidance in finding and purchasing your ideal property with complete legal and financial support.',
                                'icon' => 'fas fa-home',
                                'features' => ['Property Search', 'Legal Verification', 'Loan Assistance', 'Documentation']
                            ],
                            [
                                'title' => 'Property Selling',
                                'description' => 'Maximize your property value with our strategic marketing and negotiation expertise.',
                                'icon' => 'fas fa-handshake',
                                'features' => ['Market Analysis', 'Property Valuation', 'Marketing Strategy', 'Negotiation Support']
                            ],
                            [
                                'title' => 'Investment Consultation',
                                'description' => 'Make informed investment decisions with our market insights and portfolio management.',
                                'icon' => 'fas fa-chart-line',
                                'features' => ['Market Research', 'ROI Analysis', 'Portfolio Planning', 'Risk Assessment']
                            ],
                            [
                                'title' => 'Property Management',
                                'description' => 'Complete property management services for landlords and property investors.',
                                'icon' => 'fas fa-building',
                                'features' => ['Tenant Management', 'Maintenance', 'Rent Collection', 'Legal Compliance']
                            ]
                        ]
                    ]
                ],
                'template' => 'services',
                'is_active' => true,
                'sort_order' => 3,
                'images' => [
                    'images/pages/services/hero-bg.jpg',
                    'images/pages/services/service-1.jpg',
                    'images/pages/services/service-2.jpg',
                    'images/pages/services/service-3.jpg'
                ]
            ],
            [
                'name' => 'Blog',
                'slug' => 'blog',
                'title' => 'Real Estate Blog - Hestia Abodes Insights',
                'meta_description' => 'Stay updated with latest real estate trends, market insights, and property investment tips from Hestia Abodes experts.',
                'meta_keywords' => 'real estate blog, property news, market trends, investment tips, property insights',
                'content' => '<div class="blog-content">
                    <h2>Real Estate Insights</h2>
                    <p>Stay informed with the latest market trends, investment tips, and expert insights from our real estate professionals.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'blog_hero',
                        'title' => 'Real Estate Insights',
                        'subtitle' => 'Stay Informed with Latest Market Trends',
                        'description' => 'Expert insights, market analysis, and valuable tips to help you make informed real estate decisions.',
                        'image' => 'images/blog-hero.jpg'
                    ]
                ],
                'template' => 'blog',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Media Center',
                'slug' => 'media-center',
                'title' => 'Media Center - Hestia Abodes News & Updates',
                'meta_description' => 'Latest news, press releases, awards, and media coverage of Hestia Abodes real estate achievements.',
                'meta_keywords' => 'hestia abodes news, press releases, real estate awards, media coverage, company updates',
                'content' => '<div class="media-content">
                    <h2>Media Center</h2>
                    <p>Stay updated with our latest achievements, press releases, and industry recognition.</p>
                </div>',
                'sections' => [
                    [
                        'type' => 'hero',
                        'key' => 'media_hero',
                        'title' => 'Media Center',
                        'subtitle' => 'Latest News & Updates',
                        'description' => 'Stay updated with our latest achievements, press releases, and industry recognition.',
                        'image' => 'images/media-hero.jpg'
                    ]
                ],
                'template' => 'media',
                'is_active' => true,
                'sort_order' => 5
            ]
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }
}
