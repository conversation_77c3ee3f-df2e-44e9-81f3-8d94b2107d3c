<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ComprehensiveHomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing content
        \App\Models\HomeContent::truncate();

        // Hero Section
        $heroMain = \App\Models\HomeContent::create([
            'section' => 'hero',
            'item_type' => 'main',
            'title' => 'Find Your Dream Home with Hestia Abodes',
            'subtitle' => 'Premium Real Estate Solutions',
            'description' => 'Discover exceptional properties and personalized service that makes your real estate journey seamless and successful.',
            'image' => 'images/home/<USER>/hero-main.jpg',
            'icon' => 'fas fa-home',
            'button_text' => 'Explore Properties',
            'button_link' => '/properties',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // About Section
        $aboutMain = \App\Models\HomeContent::create([
            'section' => 'about',
            'item_type' => 'main',
            'title' => 'About Hestia Abodes',
            'subtitle' => 'Your Trusted Real Estate Partner',
            'description' => 'With years of experience in the real estate industry, Hestia Abodes has established itself as a premier destination for property buyers, sellers, and investors. Our commitment to excellence and personalized service sets us apart.',
            'image' => 'images/home/<USER>/about-main.jpg',
            'icon' => 'fas fa-building',
            'button_text' => 'Learn More',
            'button_link' => '/about',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Services Section
        $servicesMain = \App\Models\HomeContent::create([
            'section' => 'services',
            'item_type' => 'main',
            'title' => 'Our Services',
            'subtitle' => 'Comprehensive Real Estate Solutions',
            'description' => 'From buying and selling to property management and investment consulting, we offer a full range of real estate services tailored to your needs.',
            'image' => 'images/home/<USER>/services-main.jpg',
            'icon' => 'fas fa-cogs',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // Services Sub-items
        $servicesData = [
            [
                'title' => 'Property Sales',
                'description' => 'Expert guidance through the entire buying and selling process with market insights and negotiation expertise.',
                'icon' => 'fas fa-handshake',
                'image' => 'images/home/<USER>/property-sales.jpg',
            ],
            [
                'title' => 'Property Management',
                'description' => 'Comprehensive property management services including tenant screening, maintenance, and rent collection.',
                'icon' => 'fas fa-key',
                'image' => 'images/home/<USER>/property-management.jpg',
            ],
            [
                'title' => 'Investment Consulting',
                'description' => 'Strategic investment advice to help you build a profitable real estate portfolio with maximum returns.',
                'icon' => 'fas fa-chart-line',
                'image' => 'images/home/<USER>/investment-consulting.jpg',
            ],
            [
                'title' => 'Market Analysis',
                'description' => 'Detailed market research and analysis to help you make informed real estate decisions.',
                'icon' => 'fas fa-chart-bar',
                'image' => 'images/home/<USER>/market-analysis.jpg',
            ],
        ];

        foreach ($servicesData as $index => $service) {
            \App\Models\HomeContent::create([
                'parent_id' => $servicesMain->id,
                'section' => 'services',
                'item_type' => 'sub_item',
                'title' => $service['title'],
                'description' => $service['description'],
                'icon' => $service['icon'],
                'image' => $service['image'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        // Why Choose Us Section (update existing)
        $whyChooseMain = \App\Models\HomeContent::where('section', 'why_choose_us')->whereNull('parent_id')->first();
        if ($whyChooseMain) {
            $whyChooseMain->update([
                'icon' => 'fas fa-star',
                'image' => 'images/home/<USER>/why-choose-main.jpg',
            ]);

            // Update existing sub-items with icons
            $whyChooseSubItems = \App\Models\HomeContent::where('parent_id', $whyChooseMain->id)->get();
            $icons = ['fas fa-filter', 'fas fa-users', 'fas fa-clock', 'fas fa-shield-alt', 'fas fa-chart-line', 'fas fa-heart'];

            foreach ($whyChooseSubItems as $index => $item) {
                $item->update([
                    'icon' => $icons[$index] ?? 'fas fa-check-circle',
                    'image' => "images/home/<USER>/feature-" . ($index + 1) . ".jpg",
                ]);
            }
        }

        // Testimonials Section
        $testimonialsMain = \App\Models\HomeContent::create([
            'section' => 'testimonials',
            'item_type' => 'main',
            'title' => 'What Our Clients Say',
            'subtitle' => 'Real Stories from Real People',
            'description' => 'Hear from our satisfied clients about their experience working with Hestia Abodes and how we helped them achieve their real estate goals.',
            'image' => 'images/home/<USER>/testimonials-main.jpg',
            'icon' => 'fas fa-quote-left',
            'is_active' => true,
            'sort_order' => 4,
        ]);

        // Testimonials Sub-items
        $testimonialsData = [
            [
                'title' => 'Sarah Johnson',
                'subtitle' => 'First-time Home Buyer',
                'description' => 'Hestia Abodes made my first home buying experience incredibly smooth. Their team was patient, knowledgeable, and always available to answer my questions.',
                'icon' => 'fas fa-user',
                'image' => 'images/home/<USER>/client-1.jpg',
            ],
            [
                'title' => 'Michael Chen',
                'subtitle' => 'Property Investor',
                'description' => 'I have worked with Hestia Abodes on multiple investment properties. Their market insights and professional service have been invaluable to my portfolio growth.',
                'icon' => 'fas fa-user-tie',
                'image' => 'images/home/<USER>/client-2.jpg',
            ],
            [
                'title' => 'Emily Rodriguez',
                'subtitle' => 'Home Seller',
                'description' => 'Selling my home was stress-free thanks to Hestia Abodes. They handled everything professionally and got me the best price in record time.',
                'icon' => 'fas fa-user-graduate',
                'image' => 'images/home/<USER>/client-3.jpg',
            ],
        ];

        foreach ($testimonialsData as $index => $testimonial) {
            \App\Models\HomeContent::create([
                'parent_id' => $testimonialsMain->id,
                'section' => 'testimonials',
                'item_type' => 'sub_item',
                'title' => $testimonial['title'],
                'subtitle' => $testimonial['subtitle'],
                'description' => $testimonial['description'],
                'icon' => $testimonial['icon'],
                'image' => $testimonial['image'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        $this->command->info('Comprehensive home content seeded successfully!');
    }
}
