<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ComprehensiveHomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing content
        \App\Models\HomeContent::truncate();

        // Hero Section - Main Container
        $heroMain = \App\Models\HomeContent::create([
            'section' => 'hero',
            'item_type' => 'main',
            'title' => 'Hero Slider Section',
            'subtitle' => 'Dynamic Hero Content',
            'description' => 'Multiple hero slides showcasing different aspects of Hestia Abodes services.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/hero-main.jpg',
            'icon' => 'fas fa-home',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Hero Slides - All Previous Slider Content
        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'Hestia Abodes – Real Estate Solutions You Can Trust',
            'subtitle' => 'Your Trusted Real Estate Partner',
            'description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-1.jpg',
            'button_text' => 'Explore Projects',
            'button_link' => '/projects',
            'meta_data' => json_encode([
                'button_text_2' => 'Get in Touch',
                'button_link_2' => '/contact'
            ]),
            'is_active' => true,
            'sort_order' => 1,
        ]);

        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'For Home Buyers',
            'subtitle' => 'Your Dream Home Awaits',
            'description' => 'Personalized recommendations, legal guidance, and stress-free transactions. We make your home buying journey smooth and transparent.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-2.jpg',
            'button_text' => 'Our Services',
            'button_link' => '#services',
            'meta_data' => json_encode([
                'button_text_2' => 'Learn More',
                'button_link_2' => '/about-us'
            ]),
            'is_active' => true,
            'sort_order' => 2,
        ]);

        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'For Builders',
            'subtitle' => 'Exclusive Partnership Opportunities',
            'description' => 'Exclusive mandates for focused marketing and seamless sales execution. Partner with us to maximize your project\'s potential.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-3.jpg',
            'button_text' => 'Partner With Us',
            'button_link' => '#builders',
            'meta_data' => json_encode([
                'button_text_2' => 'Get Started',
                'button_link_2' => '#contact'
            ]),
            'is_active' => true,
            'sort_order' => 3,
        ]);

        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'Your Home, Your Vision—Our Expertise',
            'subtitle' => 'Premium Real Estate Consultancy',
            'description' => 'Premium real estate consultancy based in Pune, committed to helping homebuyers and investors make confident, informed decisions.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-4.jpg',
            'button_text' => 'About Us',
            'button_link' => '/about-us',
            'meta_data' => json_encode([
                'button_text_2' => 'Get in Touch',
                'button_link_2' => '/contact'
            ]),
            'is_active' => true,
            'sort_order' => 4,
        ]);

        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'Godrej Meridien',
            'subtitle' => 'Premium Residential',
            'description' => 'Luxury living in Sector 106, Gurgaon with world-class amenities and modern architecture.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-5.jpg',
            'button_text' => 'Know More',
            'button_link' => '#',
            'is_active' => true,
            'sort_order' => 5,
        ]);

        \App\Models\HomeContent::create([
            'parent_id' => $heroMain->id,
            'section' => 'hero',
            'item_type' => 'slide',
            'title' => 'M3M Heights',
            'subtitle' => 'Urban Living',
            'description' => 'Modern apartments in Sector 65, Gurgaon designed for contemporary urban lifestyle.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/slide-6.jpg',
            'button_text' => 'Know More',
            'button_link' => '#',
            'is_active' => true,
            'sort_order' => 6,
        ]);

        // About Section
        $aboutMain = \App\Models\HomeContent::create([
            'section' => 'about',
            'item_type' => 'main',
            'title' => 'About Hestia Abodes',
            'subtitle' => 'Your Trusted Real Estate Partner',
            'description' => 'With years of experience in the real estate industry, Hestia Abodes has established itself as a premier destination for property buyers, sellers, and investors. Our commitment to excellence and personalized service sets us apart.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/about-main.jpg',
            'icon' => 'fas fa-building',
            'button_text' => 'Learn More',
            'button_link' => '/about',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Services Section
        $servicesMain = \App\Models\HomeContent::create([
            'section' => 'services',
            'item_type' => 'main',
            'title' => 'Our Services',
            'subtitle' => 'Comprehensive Real Estate Solutions',
            'description' => 'From buying and selling to property management and investment consulting, we offer a full range of real estate services tailored to your needs.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/services-main.jpg',
            'icon' => 'fas fa-cogs',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // Services Sub-items
        $servicesData = [
            [
                'title' => 'Property Sales',
                'description' => 'Expert guidance through the entire buying and selling process with market insights and negotiation expertise.',
                'icon' => 'fas fa-handshake',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/property-sales.jpg',
            ],
            [
                'title' => 'Property Management',
                'description' => 'Comprehensive property management services including tenant screening, maintenance, and rent collection.',
                'icon' => 'fas fa-key',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/property-management.jpg',
            ],
            [
                'title' => 'Investment Consulting',
                'description' => 'Strategic investment advice to help you build a profitable real estate portfolio with maximum returns.',
                'icon' => 'fas fa-chart-line',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/investment-consulting.jpg',
            ],
            [
                'title' => 'Market Analysis',
                'description' => 'Detailed market research and analysis to help you make informed real estate decisions.',
                'icon' => 'fas fa-chart-bar',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/market-analysis.jpg',
            ],
        ];

        foreach ($servicesData as $index => $service) {
            \App\Models\HomeContent::create([
                'parent_id' => $servicesMain->id,
                'section' => 'services',
                'item_type' => 'sub_item',
                'title' => $service['title'],
                'description' => $service['description'],
                'icon' => $service['icon'],
                'image' => $service['image'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        // Why Choose Us Section
        $whyChooseMain = \App\Models\HomeContent::create([
            'section' => 'why_choose_us',
            'item_type' => 'main',
            'title' => 'Why Choose Hestia Abodes',
            'subtitle' => 'Your Success is Our Priority',
            'description' => 'We combine industry expertise with personalized service to deliver exceptional results for every client.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/why-choose-main.jpg',
            'icon' => 'fas fa-star',
            'is_active' => true,
            'sort_order' => 4,
        ]);

        // Why Choose Us Sub-items
        $whyChooseData = [
            [
                'title' => 'Curated Property Selection',
                'description' => 'Hand-picked properties that match your specific requirements and budget.',
                'icon' => 'fas fa-filter',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/feature-1.jpg',
            ],
            [
                'title' => 'Expert Team',
                'description' => 'Experienced professionals dedicated to your real estate success.',
                'icon' => 'fas fa-users',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/feature-2.jpg',
            ],
            [
                'title' => '24/7 Support',
                'description' => 'Round-the-clock assistance for all your real estate needs.',
                'icon' => 'fas fa-clock',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/feature-3.jpg',
            ],
            [
                'title' => 'Trusted Service',
                'description' => 'Transparent and reliable service you can count on.',
                'icon' => 'fas fa-shield-alt',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/feature-4.jpg',
            ],
        ];

        foreach ($whyChooseData as $index => $feature) {
            \App\Models\HomeContent::create([
                'parent_id' => $whyChooseMain->id,
                'section' => 'why_choose_us',
                'item_type' => 'sub_item',
                'title' => $feature['title'],
                'description' => $feature['description'],
                'icon' => $feature['icon'],
                'image' => $feature['image'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        // Testimonials Section
        $testimonialsMain = \App\Models\HomeContent::create([
            'section' => 'testimonials',
            'item_type' => 'main',
            'title' => 'What Our Clients Say',
            'subtitle' => 'Real Stories from Real People',
            'description' => 'Hear from our satisfied clients about their experience working with Hestia Abodes and how we helped them achieve their real estate goals.',
            'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/testimonials-main.jpg',
            'icon' => 'fas fa-quote-left',
            'is_active' => true,
            'sort_order' => 5,
        ]);

        // Testimonials Sub-items
        $testimonialsData = [
            [
                'title' => 'Sarah Johnson',
                'subtitle' => 'First-time Home Buyer',
                'description' => 'Hestia Abodes made my first home buying experience incredibly smooth. Their team was patient, knowledgeable, and always available to answer my questions.',
                'icon' => 'fas fa-user',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/client-1.jpg',
            ],
            [
                'title' => 'Michael Chen',
                'subtitle' => 'Property Investor',
                'description' => 'I have worked with Hestia Abodes on multiple investment properties. Their market insights and professional service have been invaluable to my portfolio growth.',
                'icon' => 'fas fa-user-tie',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/client-2.jpg',
            ],
            [
                'title' => 'Emily Rodriguez',
                'subtitle' => 'Home Seller',
                'description' => 'Selling my home was stress-free thanks to Hestia Abodes. They handled everything professionally and got me the best price in record time.',
                'icon' => 'fas fa-user-graduate',
                'image' => 'http://127.0.0.1:8001/storage/images/home/<USER>/client-3.jpg',
            ],
        ];

        foreach ($testimonialsData as $index => $testimonial) {
            \App\Models\HomeContent::create([
                'parent_id' => $testimonialsMain->id,
                'section' => 'testimonials',
                'item_type' => 'sub_item',
                'title' => $testimonial['title'],
                'subtitle' => $testimonial['subtitle'],
                'description' => $testimonial['description'],
                'icon' => $testimonial['icon'],
                'image' => $testimonial['image'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        $this->command->info('Comprehensive home content seeded successfully!');
    }
}
