<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeContent;

class HomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing content
        HomeContent::truncate();

        $homeContents = [
            [
                'section' => 'hero',
                'item_type' => 'main',
                'title' => 'Hestia Abodes',
                'subtitle' => 'Premium Real Estate Solutions',
                'description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
                'button_text' => 'Explore Projects',
                'button_link' => '/projects',
                'is_active' => true,
                'sort_order' => 1,
                'meta_title' => 'Hestia Abodes - Premium Real Estate Solutions in Pune & Mumbai',
                'meta_description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
                'meta_keywords' => 'real estate, property, investment, residential, commercial, Pune, Mumbai, Hestia Abodes',
                'og_title' => 'Hestia Abodes - Premium Real Estate Solutions',
                'og_description' => 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.',
            ],
            [
                'section' => 'about',
                'item_type' => 'main',
                'title' => 'Who We Are',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'description' => 'Hestia Abodes is a premium real estate consultancy firm based in Pune, committed to helping homebuyers and investors make confident, informed decisions in an ever-evolving property market. As trusted Channel Partners (CPs), we bridge the gap between buyers and top-tier developers by offering personalized, transparent, and end-to-end real estate advisory services. Founded on the principles of integrity, insight, and impact, we go beyond listings.',
                'button_text' => 'Learn More',
                'button_link' => '/about-us',
                'is_active' => true,
                'sort_order' => 2,
                'meta_data' => json_encode([
                    'core_values' => [
                        ['icon' => 'fas fa-shield-alt', 'title' => 'Integrity'],
                        ['icon' => 'fas fa-lightbulb', 'title' => 'Insight'],
                        ['icon' => 'fas fa-bullseye', 'title' => 'Impact']
                    ]
                ]),
            ],
            [
                'section' => 'services',
                'title' => 'Our Services',
                'subtitle' => 'Comprehensive Real Estate Solutions',
                'description' => 'From personalized property advisory to curated project shortlisting and site visits, we provide end-to-end real estate services tailored to your needs.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'section' => 'featured_projects',
                'item_type' => 'main',
                'title' => 'FEATURED PROJECTS',
                'subtitle' => 'Handpicked Premium Properties',
                'description' => 'Explore our carefully curated selection of premium residential and commercial projects across Pune and Mumbai.',
                'button_text' => 'View All Projects',
                'button_link' => '/projects',
                'is_active' => true,
                'sort_order' => 3,
                'meta_data' => json_encode([
                    'featured_projects' => [
                        [
                            'title' => 'Godrej Meridien',
                            'subtitle' => 'Premium Residential',
                            'location' => 'Sector 106, Gurgaon',
                            'possession' => 'January 2025',
                            'configuration' => '1, 2 & 3 BHK',
                            'price' => 'INR 1.62 Cr onwards',
                            'image' => 'images/hero-bg.jpg'
                        ]
                    ]
                ]),
            ],
            [
                'section' => 'why_choose_us',
                'item_type' => 'main',
                'title' => 'Why Choose Hestia Abodes',
                'subtitle' => 'Where every decision is backed by trust',
                'description' => 'We provide comprehensive real estate solutions with transparency, expertise, and personalized service.',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'section' => 'portfolio',
                'item_type' => 'main',
                'title' => 'Our Portfolio',
                'subtitle' => 'Successful Projects & Satisfied Clients',
                'description' => 'Explore our diverse portfolio of successful real estate transactions and satisfied clients across various property types.',
                'button_text' => 'View All Projects',
                'button_link' => '/portfolio',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'section' => 'testimonials',
                'item_type' => 'main',
                'title' => 'What Our Clients Say',
                'subtitle' => 'Success Stories',
                'description' => 'Read testimonials from our satisfied clients who found their dream homes with our expert guidance.',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'section' => 'services',
                'item_type' => 'main',
                'title' => 'Our Services',
                'subtitle' => 'For Home Buyers – Your Ideal Home, Simplified',
                'description' => 'From property search to investment advisory, we provide end-to-end real estate services.',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'section' => 'contact_form',
                'item_type' => 'main',
                'title' => 'Contact Us',
                'subtitle' => 'Get in Touch with Our Experts',
                'description' => 'Ready to find your dream property? Contact our expert team for personalized assistance.',
                'is_active' => true,
                'sort_order' => 8,
            ],
        ];

        foreach ($homeContents as $content) {
            HomeContent::updateOrCreate(
                ['section' => $content['section'], 'item_type' => $content['item_type'] ?? 'main'],
                $content
            );
        }

        // Add Services Sub-Content
        $servicesParent = HomeContent::where('section', 'services')->where('item_type', 'main')->first();
        if ($servicesParent) {
            $services = [
                [
                    'title' => 'Personalized Property Advisory',
                    'description' => 'Curated recommendations based on your specific needs, budget, and lifestyle preferences.',
                    'icon' => 'fas fa-user-tie',
                    'sort_order' => 1,
                ],
                [
                    'title' => 'Curated Project Shortlisting & Site Visits',
                    'description' => 'We filter the best-fit, verified projects and guide you through strategic site visits.',
                    'icon' => 'fas fa-clipboard-list',
                    'sort_order' => 2,
                ],
                [
                    'title' => 'Skilled Negotiation & Deal Support',
                    'description' => 'Expert negotiation to get you the best deal with transparent pricing and terms.',
                    'icon' => 'fas fa-handshake',
                    'sort_order' => 3,
                ],
                [
                    'title' => 'Comprehensive Legal & Documentation',
                    'description' => 'Complete legal due diligence and documentation assistance for secure transactions.',
                    'icon' => 'fas fa-file-contract',
                    'sort_order' => 4,
                ],
                [
                    'title' => 'Strategic Investment Advisory',
                    'description' => 'ROI-focused advice and market analysis to help you make informed investment decisions.',
                    'icon' => 'fas fa-chart-line',
                    'sort_order' => 5,
                ],
                [
                    'title' => 'Continued Support Beyond Sale',
                    'description' => 'Post-possession help, resale support, and long-term advisory services.',
                    'icon' => 'fas fa-headset',
                    'sort_order' => 6,
                ],
            ];

            foreach ($services as $service) {
                HomeContent::updateOrCreate(
                    [
                        'section' => 'services',
                        'item_type' => 'service',
                        'title' => $service['title'],
                        'parent_id' => $servicesParent->id
                    ],
                    [
                        'section' => 'services',
                        'item_type' => 'service',
                        'title' => $service['title'],
                        'subtitle' => null,
                        'description' => $service['description'],
                        'image' => null,
                        'icon' => $service['icon'],
                        'button_text' => 'Learn More',
                        'button_link' => '#',
                        'sort_order' => $service['sort_order'],
                        'is_active' => true,
                        'parent_id' => $servicesParent->id,
                    ]
                );
            }
        }

        // Add Why Choose Us Sub-Content
        $whyChooseUsParent = HomeContent::where('section', 'why_choose_us')->where('item_type', 'main')->first();
        if ($whyChooseUsParent) {
            $features = [
                [
                    'title' => 'Selective, Not Generic',
                    'description' => 'We only recommend what fits you, not curated approach ensures you see properties that truly match your needs and preferences.',
                    'icon' => 'fas fa-search',
                    'sort_order' => 1,
                ],
                [
                    'title' => 'We Represent You, Not the Developer',
                    'description' => 'Your goals come first. As independent consultants, we provide unbiased advice focused on your best interests.',
                    'icon' => 'fas fa-users',
                    'sort_order' => 2,
                ],
                [
                    'title' => 'Market Insight Over Marketing',
                    'description' => 'We share context, not just content. Get real market analysis and honest feedback, not sales pitches.',
                    'icon' => 'fas fa-chart-line',
                    'sort_order' => 3,
                ],
                [
                    'title' => 'Process-Driven',
                    'description' => 'You always know what\'s happening next. Our systematic approach ensures transparency at every step.',
                    'icon' => 'fas fa-list-check',
                    'sort_order' => 4,
                ],
                [
                    'title' => 'Compliance-First',
                    'description' => 'We protect your investment with legal clarity. All documentation and RERA compliance handled meticulously.',
                    'icon' => 'fas fa-shield-alt',
                    'sort_order' => 5,
                ],
                [
                    'title' => 'Here for the Long Haul',
                    'description' => 'Even after deal closure, we remain your advisors. Long-term relationship matter more than quick sales.',
                    'icon' => 'fas fa-handshake',
                    'sort_order' => 6,
                ],
            ];

            foreach ($features as $feature) {
                HomeContent::updateOrCreate(
                    [
                        'section' => 'why_choose_us',
                        'item_type' => 'feature',
                        'title' => $feature['title'],
                        'parent_id' => $whyChooseUsParent->id
                    ],
                    [
                        'section' => 'why_choose_us',
                        'item_type' => 'feature',
                        'title' => $feature['title'],
                        'subtitle' => null,
                        'description' => $feature['description'],
                        'image' => null,
                        'icon' => $feature['icon'],
                        'button_text' => null,
                        'button_link' => null,
                        'sort_order' => $feature['sort_order'],
                        'is_active' => true,
                        'parent_id' => $whyChooseUsParent->id,
                    ]
                );
            }
        }

        // Add Portfolio Sub-Content
        $portfolioParent = HomeContent::where('section', 'portfolio')->where('item_type', 'main')->first();
        if ($portfolioParent) {
            $portfolioItems = [
                [
                    'title' => 'Luxury Apartments',
                    'description' => 'Premium residential projects with world-class amenities',
                    'icon' => 'fas fa-building',
                    'image' => 'images/portfolio/luxury-apartments.jpg',
                    'sort_order' => 1,
                ],
                [
                    'title' => 'Commercial Spaces',
                    'description' => 'Strategic commercial properties for business growth',
                    'icon' => 'fas fa-briefcase',
                    'image' => 'images/portfolio/commercial-spaces.jpg',
                    'sort_order' => 2,
                ],
                [
                    'title' => 'Villa Projects',
                    'description' => 'Exclusive villa communities with premium lifestyle',
                    'icon' => 'fas fa-home',
                    'image' => 'images/portfolio/villa-projects.jpg',
                    'sort_order' => 3,
                ],
                [
                    'title' => 'Investment Properties',
                    'description' => 'High-ROI properties for smart investors',
                    'icon' => 'fas fa-chart-line',
                    'image' => 'images/portfolio/investment-properties.jpg',
                    'sort_order' => 4,
                ],
                [
                    'title' => 'Affordable Housing',
                    'description' => 'Quality homes within budget for first-time buyers',
                    'icon' => 'fas fa-heart',
                    'image' => 'images/portfolio/affordable-housing.jpg',
                    'sort_order' => 5,
                ],
                [
                    'title' => 'Plots & Land',
                    'description' => 'Prime land parcels for custom development',
                    'icon' => 'fas fa-map',
                    'image' => 'images/portfolio/plots-land.jpg',
                    'sort_order' => 6,
                ],
            ];

            foreach ($portfolioItems as $item) {
                HomeContent::updateOrCreate(
                    [
                        'section' => 'portfolio',
                        'item_type' => 'portfolio_item',
                        'title' => $item['title'],
                        'parent_id' => $portfolioParent->id
                    ],
                    [
                        'section' => 'portfolio',
                        'item_type' => 'portfolio_item',
                        'title' => $item['title'],
                        'subtitle' => null,
                        'description' => $item['description'],
                        'image' => $item['image'],
                        'icon' => $item['icon'],
                        'button_text' => 'View Details',
                        'button_link' => '#',
                        'sort_order' => $item['sort_order'],
                        'is_active' => true,
                        'parent_id' => $portfolioParent->id,
                    ]
                );
            }
        }

        // Add Projects by Location Section
        $projectsLocationParent = HomeContent::updateOrCreate(
            ['section' => 'projects_by_location', 'item_type' => 'main'],
            [
                'section' => 'projects_by_location',
                'item_type' => 'main',
                'title' => 'Explore Projects in Your Desired Locations',
                'subtitle' => 'Find Properties Across Prime Locations',
                'description' => 'Discover premium real estate opportunities in India\'s most sought-after cities.',
                'image' => null,
                'icon' => 'fas fa-map-marked-alt',
                'button_text' => null,
                'button_link' => null,
                'sort_order' => 4,
                'is_active' => true,
                'parent_id' => null,
            ]
        );

        if ($projectsLocationParent) {
            $locations = [
                [
                    'title' => 'Mumbai',
                    'subtitle' => '15 Projects Available',
                    'description' => '15 Projects Available',
                    'icon' => 'fas fa-city',
                    'image' => 'images/locations/mumbai.jpg',
                    'button_text' => 'Explore Mumbai',
                    'sort_order' => 1,
                ],
                [
                    'title' => 'Pune',
                    'subtitle' => '12 Projects Available',
                    'description' => '12 Projects Available',
                    'icon' => 'fas fa-building',
                    'image' => 'images/locations/pune.jpg',
                    'button_text' => 'Explore Pune',
                    'sort_order' => 2,
                ],
                [
                    'title' => 'Goa',
                    'subtitle' => '8 Projects Available',
                    'description' => '8 Projects Available',
                    'icon' => 'fas fa-umbrella-beach',
                    'image' => 'images/locations/goa.jpg',
                    'button_text' => 'Explore Goa',
                    'sort_order' => 3,
                ],
            ];

            foreach ($locations as $location) {
                HomeContent::updateOrCreate(
                    [
                        'section' => 'projects_by_location',
                        'item_type' => 'location',
                        'title' => $location['title'],
                        'parent_id' => $projectsLocationParent->id
                    ],
                    [
                        'section' => 'projects_by_location',
                        'item_type' => 'location',
                        'title' => $location['title'],
                        'subtitle' => $location['subtitle'],
                        'description' => $location['description'],
                        'image' => $location['image'],
                        'icon' => $location['icon'],
                        'button_text' => $location['button_text'],
                        'button_link' => '#',
                        'sort_order' => $location['sort_order'],
                        'is_active' => true,
                        'parent_id' => $projectsLocationParent->id,
                    ]
                );
            }
        }

        // Update sort orders to match the layout
        $sortOrderUpdates = [
            ['section' => 'hero', 'sort_order' => 1],
            ['section' => 'about', 'sort_order' => 2],
            ['section' => 'featured_projects', 'sort_order' => 3],
            ['section' => 'projects_by_location', 'sort_order' => 4],
            ['section' => 'why_choose_us', 'sort_order' => 5],
            ['section' => 'services', 'sort_order' => 6],
            ['section' => 'portfolio', 'sort_order' => 7],
            ['section' => 'testimonials', 'sort_order' => 8],
            ['section' => 'contact_form', 'sort_order' => 9],
        ];

        foreach ($sortOrderUpdates as $update) {
            HomeContent::where('section', $update['section'])
                ->whereNull('parent_id')
                ->update(['sort_order' => $update['sort_order']]);
        }
    }
}
