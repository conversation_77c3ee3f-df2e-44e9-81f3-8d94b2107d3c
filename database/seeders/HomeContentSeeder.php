<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeContent;

class HomeContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $homeContents = [
            [
                'section' => 'hero',
                'title' => 'Find Your Dream Home with Hestia Abodes',
                'subtitle' => 'Premium Real Estate Solutions',
                'description' => 'Discover premium residential and commercial properties with Hestia Abodes. Your trusted partner in real estate investment and property management.',
                'button_text' => 'Explore Properties',
                'button_link' => '/projects',
                'is_active' => true,
                'sort_order' => 1,
                'meta_title' => 'Hestia Abodes - Premium Real Estate Solutions in Pune & Mumbai',
                'meta_description' => 'Discover premium residential and commercial properties with Hestia Abodes. Your trusted partner in real estate investment and property management in Pune and Mumbai.',
                'meta_keywords' => 'real estate, property, investment, residential, commercial, Pune, Mumbai, Hestia Abodes',
                'og_title' => 'Hestia Abodes - Premium Real Estate Solutions',
                'og_description' => 'Discover premium residential and commercial properties with Hestia Abodes. Your trusted partner in real estate investment and property management.',
            ],
            [
                'section' => 'about',
                'title' => 'Who We Are',
                'subtitle' => 'Your Trusted Real Estate Partner',
                'description' => 'Hestia Abodes is a premium real estate consultancy firm based in Pune, committed to helping homebuyers and investors make confident, informed decisions in an ever-evolving property market.',
                'button_text' => 'Learn More',
                'button_link' => '/about-us',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'section' => 'services',
                'title' => 'Our Services',
                'subtitle' => 'Comprehensive Real Estate Solutions',
                'description' => 'From personalized property advisory to curated project shortlisting and site visits, we provide end-to-end real estate services tailored to your needs.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'section' => 'projects',
                'title' => 'Featured Projects',
                'subtitle' => 'Handpicked Premium Properties',
                'description' => 'Explore our carefully curated selection of premium residential and commercial projects across Pune and Mumbai.',
                'button_text' => 'View All Projects',
                'button_link' => '/projects',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'section' => 'testimonials',
                'title' => 'What Our Clients Say',
                'subtitle' => 'Success Stories',
                'description' => 'Read testimonials from our satisfied clients who found their dream homes with our expert guidance.',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'section' => 'why_choose_us',
                'title' => 'Why Choose Hestia Abodes?',
                'subtitle' => 'Your Success is Our Priority',
                'description' => 'With years of experience and deep market knowledge, we provide personalized service, transparent processes, and expert guidance throughout your property journey.',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'section' => 'cta',
                'title' => 'Ready to Find Your Dream Property?',
                'subtitle' => 'Get Started Today',
                'description' => 'Contact our expert team for personalized property consultation and discover the perfect property that matches your needs and budget.',
                'button_text' => 'Contact Us Now',
                'button_link' => '/contact',
                'is_active' => true,
                'sort_order' => 7,
            ],
        ];

        foreach ($homeContents as $content) {
            HomeContent::updateOrCreate(
                ['section' => $content['section']],
                $content
            );
        }
    }
}
