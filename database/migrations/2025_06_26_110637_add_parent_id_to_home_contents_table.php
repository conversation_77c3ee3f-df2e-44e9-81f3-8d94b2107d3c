<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->nullable()->after('id');
            $table->string('item_type')->default('main')->after('section'); // main, sub_item, feature, etc.
            $table->foreign('parent_id')->references('id')->on('home_contents')->onDelete('cascade');

            // Make section not unique anymore since we can have multiple items per section
            $table->dropUnique(['section']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('home_contents', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn(['parent_id', 'item_type']);
            $table->unique('section');
        });
    }
};
