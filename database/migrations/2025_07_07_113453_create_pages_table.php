<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Page name (About Us, Contact, etc.)
            $table->string('slug')->unique(); // URL slug (about-us, contact, etc.)
            $table->string('title'); // Page title for SEO
            $table->text('meta_description')->nullable(); // SEO meta description
            $table->text('meta_keywords')->nullable(); // SEO keywords
            $table->longText('content')->nullable(); // Main page content
            $table->json('sections')->nullable(); // Dynamic sections data
            $table->string('template')->default('default'); // Template to use
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
