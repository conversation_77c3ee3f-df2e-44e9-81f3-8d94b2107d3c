<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('page_sections', function (Blueprint $table) {
            // Basic section fields
            $table->string('icon')->nullable()->after('content');
            $table->string('icon_type')->default('fas')->after('icon'); // fas, far, fab, etc.
            $table->string('button_text')->nullable()->after('icon_type');
            $table->string('button_url')->nullable()->after('button_text');

            // Additional fields for different section types
            $table->string('subtitle')->nullable()->after('title');
            $table->text('description')->nullable()->after('content');
            $table->string('background_color', 7)->nullable()->after('description'); // hex color
            $table->string('text_color', 7)->nullable()->after('background_color'); // hex color
            $table->boolean('show_stats')->default(false)->after('text_color');
            $table->json('stats')->nullable()->after('show_stats'); // for stats sections
            $table->json('items')->nullable()->after('stats'); // for feature lists, services, etc.
            $table->string('cta_text')->nullable()->after('items');
            $table->string('cta_url', 500)->nullable()->after('cta_text');
            $table->string('secondary_cta_text')->nullable()->after('cta_url');
            $table->string('secondary_cta_url', 500)->nullable()->after('secondary_cta_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('page_sections', function (Blueprint $table) {
            $table->dropColumn([
                'icon', 'icon_type', 'button_text', 'button_url',
                'subtitle', 'description', 'background_color', 'text_color',
                'show_stats', 'stats', 'items', 'cta_text', 'cta_url',
                'secondary_cta_text', 'secondary_cta_url'
            ]);
        });
    }
};
