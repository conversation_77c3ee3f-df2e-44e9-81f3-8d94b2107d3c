/* Icon Picker Styles */
.icon-picker-wrapper {
    position: relative;
    width: 100%;
}

.icon-picker-display .input-group-text.icon-preview {
    min-width: 50px;
    justify-content: center;
    background: var(--bs-light);
    border-color: var(--bs-border-color);
}

.icon-picker-display .input-group-text.icon-preview i {
    font-size: 1.2rem;
    color: var(--bs-primary);
}

.icon-picker-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1050;
    max-height: 400px;
    display: none;
    overflow: hidden;
}

.icon-picker-dropdown.show {
    display: block;
}

.icon-picker-header {
    padding: 0.75rem;
    border-bottom: 1px solid var(--bs-border-color);
    background: var(--bs-light);
}

.icon-picker-categories {
    display: flex;
    flex-direction: column;
    height: 350px;
}

.icon-category-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.5rem;
    border-bottom: 1px solid var(--bs-border-color);
    background: var(--bs-gray-50);
    max-height: 80px;
    overflow-y: auto;
}

.category-tab {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    white-space: nowrap;
    border-radius: 0.25rem;
}

.category-tab.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
    padding: 0.75rem;
    overflow-y: auto;
    flex: 1;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 0.5rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    text-align: center;
    min-height: 70px;
}

.icon-item:hover {
    border-color: var(--bs-primary);
    background: var(--bs-primary-bg-subtle);
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.icon-item i {
    font-size: 1.5rem;
    color: var(--bs-primary);
    margin-bottom: 0.25rem;
}

.icon-item small {
    font-size: 0.7rem;
    color: var(--bs-secondary);
    line-height: 1.2;
    word-break: break-word;
}

.icon-item:hover small {
    color: var(--bs-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .icon-picker-dropdown {
        max-height: 300px;
    }
    
    .icon-picker-categories {
        height: 250px;
    }
    
    .icon-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 0.25rem;
        padding: 0.5rem;
    }
    
    .icon-item {
        padding: 0.5rem 0.25rem;
        min-height: 60px;
    }
    
    .icon-item i {
        font-size: 1.25rem;
    }
    
    .icon-item small {
        font-size: 0.65rem;
    }
    
    .category-tab {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Custom scrollbar for icon grid */
.icon-grid::-webkit-scrollbar {
    width: 6px;
}

.icon-grid::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb:hover {
    background: var(--bs-gray-500);
}

/* Custom scrollbar for category tabs */
.icon-category-tabs::-webkit-scrollbar {
    height: 4px;
}

.icon-category-tabs::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 2px;
}

.icon-category-tabs::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 2px;
}

.icon-category-tabs::-webkit-scrollbar-thumb:hover {
    background: var(--bs-gray-500);
}

/* Animation for dropdown */
.icon-picker-dropdown {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.icon-picker-dropdown.show {
    opacity: 1;
    transform: translateY(0);
}

/* Focus states */
.icon-search:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Loading state */
.icon-grid.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.icon-grid.loading::before {
    content: "Loading icons...";
    color: var(--bs-secondary);
    font-style: italic;
}

/* Empty state */
.icon-grid:empty::before {
    content: "No icons found";
    color: var(--bs-secondary);
    font-style: italic;
    grid-column: 1 / -1;
    text-align: center;
    padding: 2rem;
}

/* Selected icon highlight */
.icon-item.selected {
    border-color: var(--bs-success);
    background: var(--bs-success-bg-subtle);
}

.icon-item.selected i {
    color: var(--bs-success);
}

.icon-item.selected small {
    color: var(--bs-success);
}
