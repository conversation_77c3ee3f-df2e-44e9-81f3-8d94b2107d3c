/* Navbar Transparent Black Override */
.navbar {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-bottom: none !important;
    transition: all 0.3s ease !important;
    z-index: 1030 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    pointer-events: auto !important;
    transform: translateY(0) !important;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border-bottom: none !important;
    transition: all 0.3s ease !important;
    z-index: 1030 !important;
    padding: 15px 0 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
}

.navbar.navbar-hidden {
    transform: translateY(-100%) !important;
}

/* Ensure smooth transitions and prevent layout shift */
.navbar {
    will-change: transform !important;
}

/* Prevent dropdown issues when navbar is hidden */
.navbar.navbar-hidden .dropdown-menu {
    display: none !important;
}

.navbar.scrolled .navbar-brand {
    background: #8b751d !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.navbar.scrolled .navbar-logo {
    filter: brightness(0) invert(1) drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5)) !important;
}
