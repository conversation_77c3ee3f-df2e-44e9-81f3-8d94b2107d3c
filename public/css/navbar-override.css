/* Navbar Transparent Default, Background on Scroll */
.navbar {
    background: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border-bottom: none !important;
    transition: all 0.4s ease !important;
    z-index: 1030 !important;
    box-shadow: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    pointer-events: auto !important;
    transform: translateY(0) !important;
    padding: 20px 0 !important;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border-bottom: none !important;
    transition: all 0.4s ease !important;
    z-index: 1030 !important;
    padding: 10px 0 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

.navbar.navbar-hidden {
    transform: translateY(-100%) !important;
}

/* Ensure smooth transitions and prevent layout shift */
.navbar {
    will-change: transform !important;
}

/* Prevent dropdown issues when navbar is hidden */
.navbar.navbar-hidden .dropdown-menu {
    display: none !important;
}

/* Logo and Brand Styling */
.navbar-brand {
    transition: all 0.4s ease !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
}

.navbar .navbar-brand {
    background: transparent !important;
    border: none !important;
    transform: scale(1) !important;
}

.navbar.scrolled .navbar-brand {
    background: rgba(139, 117, 29, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transform: scale(0.9) !important;
}

/* Logo Image Styling */
.navbar-logo {
    transition: all 0.4s ease !important;
    height: 50px !important;
    width: auto !important;
}

.navbar .navbar-logo {
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.7)) !important;
    height: 50px !important;
}

.navbar.scrolled .navbar-logo {
    filter: brightness(0) invert(1) drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.5)) !important;
    height: 38px !important;
}

/* Navbar Hidden State Enhancement */
.navbar.navbar-hidden {
    transform: translateY(-100%) !important;
    transition: transform 0.4s ease !important;
}

/* Navigation Links Styling */
.navbar .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7) !important;
    transition: all 0.3s ease !important;
}

.navbar .navbar-nav .nav-link:hover {
    color: #f8c146 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.navbar.scrolled .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: none !important;
}

.navbar.scrolled .navbar-nav .nav-link:hover {
    color: #f8c146 !important;
    text-shadow: none !important;
}

/* Mobile Toggle Button */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
}

.navbar .navbar-toggler {
    background: rgba(0, 0, 0, 0.3) !important;
}

.navbar.scrolled .navbar-toggler {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.navbar-toggler-icon {
    filter: brightness(0) invert(1) !important;
}
