// Icon Picker Component
class IconPicker {
    constructor(inputElement, options = {}) {
        this.input = inputElement;
        this.options = {
            placeholder: 'Search icons...',
            maxResults: 50,
            ...options
        };
        
        // Font Awesome icon categories
        this.iconCategories = {
            'Business & Finance': [
                'fas fa-chart-line', 'fas fa-chart-bar', 'fas fa-chart-pie', 'fas fa-coins',
                'fas fa-dollar-sign', 'fas fa-euro-sign', 'fas fa-pound-sign', 'fas fa-yen-sign',
                'fas fa-credit-card', 'fas fa-wallet', 'fas fa-piggy-bank', 'fas fa-handshake',
                'fas fa-briefcase', 'fas fa-building', 'fas fa-industry', 'fas fa-store',
                'fas fa-shopping-cart', 'fas fa-cash-register', 'fas fa-receipt', 'fas fa-calculator'
            ],
            'Real Estate': [
                'fas fa-home', 'fas fa-house-user', 'fas fa-building', 'fas fa-city',
                'fas fa-warehouse', 'fas fa-store-alt', 'fas fa-hotel', 'fas fa-hospital',
                'fas fa-school', 'fas fa-university', 'fas fa-church', 'fas fa-mosque',
                'fas fa-synagogue', 'fas fa-kaaba', 'fas fa-landmark', 'fas fa-monument',
                'fas fa-key', 'fas fa-door-open', 'fas fa-door-closed', 'fas fa-lock'
            ],
            'Communication': [
                'fas fa-phone', 'fas fa-mobile-alt', 'fas fa-envelope', 'fas fa-comment',
                'fas fa-comments', 'fas fa-sms', 'fas fa-inbox', 'fas fa-paper-plane',
                'fas fa-bell', 'fas fa-bullhorn', 'fas fa-microphone', 'fas fa-volume-up',
                'fas fa-headphones', 'fas fa-satellite-dish', 'fas fa-wifi', 'fas fa-signal'
            ],
            'Technology': [
                'fas fa-laptop', 'fas fa-desktop', 'fas fa-tablet-alt', 'fas fa-mobile',
                'fas fa-keyboard', 'fas fa-mouse', 'fas fa-server', 'fas fa-database',
                'fas fa-cloud', 'fas fa-download', 'fas fa-upload', 'fas fa-sync',
                'fas fa-cog', 'fas fa-cogs', 'fas fa-wrench', 'fas fa-screwdriver'
            ],
            'Navigation & UI': [
                'fas fa-arrow-up', 'fas fa-arrow-down', 'fas fa-arrow-left', 'fas fa-arrow-right',
                'fas fa-chevron-up', 'fas fa-chevron-down', 'fas fa-chevron-left', 'fas fa-chevron-right',
                'fas fa-angle-up', 'fas fa-angle-down', 'fas fa-angle-left', 'fas fa-angle-right',
                'fas fa-plus', 'fas fa-minus', 'fas fa-times', 'fas fa-check',
                'fas fa-search', 'fas fa-filter', 'fas fa-sort', 'fas fa-list'
            ],
            'People & Users': [
                'fas fa-user', 'fas fa-users', 'fas fa-user-tie', 'fas fa-user-graduate',
                'fas fa-user-md', 'fas fa-user-nurse', 'fas fa-user-shield', 'fas fa-user-cog',
                'fas fa-id-card', 'fas fa-address-card', 'fas fa-portrait', 'fas fa-child',
                'fas fa-baby', 'fas fa-male', 'fas fa-female', 'fas fa-restroom'
            ],
            'Services': [
                'fas fa-tools', 'fas fa-hammer', 'fas fa-wrench', 'fas fa-screwdriver',
                'fas fa-paint-brush', 'fas fa-palette', 'fas fa-cut', 'fas fa-broom',
                'fas fa-spray-can', 'fas fa-fire-extinguisher', 'fas fa-first-aid',
                'fas fa-ambulance', 'fas fa-truck', 'fas fa-shipping-fast'
            ],
            'Security & Trust': [
                'fas fa-shield-alt', 'fas fa-lock', 'fas fa-unlock', 'fas fa-key',
                'fas fa-fingerprint', 'fas fa-eye', 'fas fa-eye-slash', 'fas fa-user-secret',
                'fas fa-certificate', 'fas fa-award', 'fas fa-medal', 'fas fa-trophy',
                'fas fa-star', 'fas fa-heart', 'fas fa-thumbs-up', 'fas fa-check-circle'
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createPickerHTML();
        this.bindEvents();
        this.populateIcons();
    }
    
    createPickerHTML() {
        const wrapper = document.createElement('div');
        wrapper.className = 'icon-picker-wrapper position-relative';
        
        // Insert wrapper after input
        this.input.parentNode.insertBefore(wrapper, this.input.nextSibling);
        
        // Move input into wrapper
        wrapper.appendChild(this.input);
        
        // Add icon display and trigger button
        const iconDisplay = document.createElement('div');
        iconDisplay.className = 'icon-picker-display';
        iconDisplay.innerHTML = `
            <div class="input-group">
                <span class="input-group-text icon-preview">
                    <i class="${this.input.value || 'fas fa-icons'}" id="icon-preview"></i>
                </span>
                <input type="text" class="form-control" placeholder="Selected icon class" readonly value="${this.input.value}">
                <button type="button" class="btn btn-outline-secondary" id="icon-picker-btn">
                    <i class="fas fa-palette"></i> Choose Icon
                </button>
            </div>
        `;
        
        // Add dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'icon-picker-dropdown';
        dropdown.innerHTML = `
            <div class="icon-picker-header">
                <input type="text" class="form-control icon-search" placeholder="${this.options.placeholder}">
            </div>
            <div class="icon-picker-categories">
                <div class="icon-category-tabs"></div>
                <div class="icon-grid"></div>
            </div>
        `;
        
        wrapper.appendChild(iconDisplay);
        wrapper.appendChild(dropdown);
        
        // Store references
        this.wrapper = wrapper;
        this.dropdown = dropdown;
        this.iconPreview = iconDisplay.querySelector('#icon-preview');
        this.iconDisplay = iconDisplay.querySelector('input[readonly]');
        this.searchInput = dropdown.querySelector('.icon-search');
        this.categoryTabs = dropdown.querySelector('.icon-category-tabs');
        this.iconGrid = dropdown.querySelector('.icon-grid');
        this.toggleBtn = iconDisplay.querySelector('#icon-picker-btn');
        
        // Hide original input
        this.input.style.display = 'none';
    }
    
    bindEvents() {
        // Toggle dropdown
        this.toggleBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleDropdown();
        });
        
        // Search functionality
        this.searchInput.addEventListener('input', (e) => {
            this.filterIcons(e.target.value);
        });
        
        // Close on outside click
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }
    
    populateIcons() {
        // Create category tabs
        Object.keys(this.iconCategories).forEach((category, index) => {
            const tab = document.createElement('button');
            tab.className = `btn btn-sm btn-outline-primary category-tab ${index === 0 ? 'active' : ''}`;
            tab.textContent = category;
            tab.addEventListener('click', () => this.showCategory(category));
            this.categoryTabs.appendChild(tab);
        });
        
        // Show first category by default
        this.showCategory(Object.keys(this.iconCategories)[0]);
    }
    
    showCategory(categoryName) {
        // Update active tab
        this.categoryTabs.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.textContent === categoryName);
        });
        
        // Show icons for category
        const icons = this.iconCategories[categoryName];
        this.renderIcons(icons);
    }
    
    renderIcons(icons) {
        this.iconGrid.innerHTML = '';
        
        icons.slice(0, this.options.maxResults).forEach(iconClass => {
            const iconItem = document.createElement('div');
            iconItem.className = 'icon-item';
            iconItem.innerHTML = `
                <i class="${iconClass}"></i>
                <small>${iconClass.split(' ').pop().replace('fa-', '')}</small>
            `;
            iconItem.addEventListener('click', () => this.selectIcon(iconClass));
            this.iconGrid.appendChild(iconItem);
        });
    }
    
    filterIcons(searchTerm) {
        if (!searchTerm.trim()) {
            this.showCategory(this.categoryTabs.querySelector('.category-tab.active').textContent);
            return;
        }
        
        const allIcons = Object.values(this.iconCategories).flat();
        const filteredIcons = allIcons.filter(icon => 
            icon.toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        this.renderIcons(filteredIcons);
    }
    
    selectIcon(iconClass) {
        this.input.value = iconClass;
        this.iconPreview.className = iconClass;
        this.iconDisplay.value = iconClass;
        this.closeDropdown();
        
        // Trigger change event
        this.input.dispatchEvent(new Event('change'));
    }
    
    toggleDropdown() {
        this.dropdown.classList.toggle('show');
    }
    
    closeDropdown() {
        this.dropdown.classList.remove('show');
    }
}

// Auto-initialize icon pickers
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.icon-picker').forEach(input => {
        new IconPicker(input);
    });
});
