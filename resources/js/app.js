import './bootstrap';
import '../css/app.css';

// Import Bootstrap JavaScript
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

// Import jQuery
import $ from 'jquery';
window.$ = window.jQuery = $;

// Import Swiper
import { Swiper } from 'swiper';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

// Hestia Abodes Custom JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Hestia Abodes JavaScript loaded successfully');

    // Test if Bootstrap is loaded
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap is loaded');
    } else {
        console.error('Bootstrap is not loaded');
    }

    // Navbar scroll effect with hide/show functionality
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    let scrollThreshold = 50; // Minimum scroll distance to trigger hide/show
    let ticking = false; // For performance optimization

    function updateNavbar() {
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Add/remove scrolled class for styling
        if (currentScrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar based on scroll direction
        if (currentScrollTop > scrollThreshold) {
            if (currentScrollTop > lastScrollTop && Math.abs(currentScrollTop - lastScrollTop) > 5) {
                // Scrolling down - hide navbar (with minimum scroll distance to prevent jitter)
                navbar.classList.add('navbar-hidden');
            } else if (currentScrollTop < lastScrollTop) {
                // Scrolling up - show navbar
                navbar.classList.remove('navbar-hidden');
            }
        } else {
            // At the top - always show navbar
            navbar.classList.remove('navbar-hidden');
        }

        lastScrollTop = currentScrollTop <= 0 ? 0 : currentScrollTop;
        ticking = false;
    }

    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateNavbar);
            ticking = true;
        }
    });

    // Search functionality
    const searchIcon = document.getElementById('searchIcon');
    const searchInput = document.getElementById('searchInput');

    if (searchIcon && searchInput) {
        searchIcon.addEventListener('click', function() {
            searchInput.classList.toggle('show');
            if (searchInput.classList.contains('show')) {
                searchInput.focus();
            }
        });
    }

    // Bootstrap dropdown initialization
    const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    if (dropdownElements.length > 0) {
        console.log('Found dropdown elements:', dropdownElements.length);
    }

    // Test navbar links specifically
    const navbarLinks = document.querySelectorAll('.navbar-nav .nav-link');
    console.log('Found navbar links:', navbarLinks.length);
    navbarLinks.forEach((link, index) => {
        console.log(`Navbar link ${index}:`, link.href, link.textContent.trim());

        // Add click event listener to test
        link.addEventListener('click', function(e) {
            console.log('Navbar link clicked successfully:', this.href);

            // Force navigation if needed
            if (this.href && !this.href.includes('#')) {
                console.log('Navigating to:', this.href);
                window.location.href = this.href;
            }
        });
    });

    // Sidebar functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const sidebarClose = document.getElementById('sidebarClose');

    function openSidebar() {
        console.log('Opening sidebar');
        if (sidebar && sidebarOverlay) {
            sidebar.classList.add('show');
            sidebarOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        } else {
            console.error('Sidebar elements not found');
        }
    }

    function closeSidebar() {
        console.log('Closing sidebar');
        if (sidebar && sidebarOverlay) {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    if (sidebarToggle) {
        console.log('Desktop sidebar toggle found');
        sidebarToggle.addEventListener('click', openSidebar);
    }

    if (mobileSidebarToggle) {
        console.log('Mobile sidebar toggle found');
        mobileSidebarToggle.addEventListener('click', openSidebar);
    }

    if (sidebarClose) {
        sidebarClose.addEventListener('click', closeSidebar);
    }

    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeSidebar);
    }

    // Hero Slider
    const heroSlider = new Swiper('.hero-slider', {
        modules: [Navigation, Pagination, Autoplay],
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    });

    // Featured Projects Full Screen Slider
    const featuredProjectsSlider = new Swiper('.featured-projects-slider', {
        modules: [Navigation, Pagination, Autoplay, EffectFade],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
            delay: 6000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.featured-projects-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.featured-projects-nav .swiper-button-next',
            prevEl: '.featured-projects-nav .swiper-button-prev',
        },
        effect: 'fade',
        fadeEffect: {
            crossFade: true,
        },
        speed: 1000,
    });

    // Testimonials Slider
    const testimonialsSlider = new Swiper('.testimonials-slider', {
        modules: [Pagination, Autoplay],
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 6000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.testimonials-pagination',
            clickable: true,
        },
        breakpoints: {
            768: {
                slidesPerView: 2,
            },
            1024: {
                slidesPerView: 3,
            },
        },
    });

    // Smooth scrolling for anchor links (only for same-page anchors)
    document.querySelectorAll('a[href^="#"]:not([href="#"]):not(.nav-link)').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            // Only prevent default for valid anchor links on the same page, but not navbar links
            const href = this.getAttribute('href');
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Contact form submission (handled below with more comprehensive validation)

    // Newsletter subscription
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            // Add newsletter subscription logic here
            alert('Thank you for subscribing to our newsletter!');
        });
    }

    // Project Filters
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectItems = document.querySelectorAll('.project-item');

    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filterValue = this.getAttribute('data-filter');

                // Show/hide project items based on filter
                projectItems.forEach(item => {
                    if (filterValue === 'all') {
                        item.style.display = 'block';
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        const categories = item.getAttribute('data-category').split(' ');
                        if (categories.includes(filterValue)) {
                            item.style.display = 'block';
                            setTimeout(() => {
                                item.style.opacity = '1';
                                item.style.transform = 'translateY(0)';
                            }, 100);
                        } else {
                            item.style.opacity = '0';
                            item.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                item.style.display = 'none';
                            }, 300);
                        }
                    }
                });
            });
        });
    }

    // Contact Form Handling
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Basic validation
            if (!data.firstName || !data.lastName || !data.email || !data.phone || !data.interest || !data.message) {
                alert('Please fill in all required fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Phone validation (basic)
            const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(data.phone)) {
                alert('Please enter a valid phone number.');
                return;
            }

            // Show success message
            alert('Thank you for your message! We will get back to you within 24 hours.');

            // Reset form
            this.reset();

            // Here you would typically send the data to your backend
            console.log('Contact form data:', data);
        });
    }

    // Schedule Meeting Form Handling
    const scheduleForm = document.getElementById('scheduleForm');
    if (scheduleForm) {
        scheduleForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const name = document.getElementById('meetingName').value;
            const phone = document.getElementById('meetingPhone').value;
            const date = document.getElementById('meetingDate').value;
            const time = document.getElementById('meetingTime').value;
            const type = document.getElementById('meetingType').value;
            const purpose = document.getElementById('meetingPurpose').value;

            // Basic validation
            if (!name || !phone || !date || !time || !type) {
                alert('Please fill in all required fields.');
                return;
            }

            // Date validation (not in the past)
            const selectedDate = new Date(date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                alert('Please select a future date.');
                return;
            }

            // Show success message
            alert('Meeting scheduled successfully! We will confirm the appointment via phone call.');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
            modal.hide();

            // Reset form
            this.reset();

            // Here you would typically send the data to your backend
            console.log('Schedule meeting data:', {
                name, phone, date, time, type, purpose
            });
        });
    }

    // Set minimum date for meeting scheduler
    const meetingDateInput = document.getElementById('meetingDate');
    if (meetingDateInput) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        meetingDateInput.min = tomorrow.toISOString().split('T')[0];
    }

    // Blog Category Filters
    const categoryButtons = document.querySelectorAll('.category-btn');
    const blogItems = document.querySelectorAll('.blog-item');

    if (categoryButtons.length > 0) {
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                categoryButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filterValue = this.getAttribute('data-category');

                // Show/hide blog items based on filter
                blogItems.forEach(item => {
                    if (filterValue === 'all') {
                        item.classList.remove('hidden');
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        const categories = item.getAttribute('data-category').split(' ');
                        if (categories.includes(filterValue)) {
                            item.classList.remove('hidden');
                            setTimeout(() => {
                                item.style.opacity = '1';
                                item.style.transform = 'translateY(0)';
                            }, 100);
                        } else {
                            item.style.opacity = '0';
                            item.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                item.classList.add('hidden');
                            }, 300);
                        }
                    }
                });
            });
        });
    }

    // Blog Newsletter Subscription
    const blogNewsletterForm = document.getElementById('blogNewsletterForm');
    if (blogNewsletterForm) {
        blogNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value;

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Show success message
            alert('Thank you for subscribing! You will receive our latest insights and market updates.');

            // Reset form
            emailInput.value = '';

            // Here you would typically send the data to your backend
            console.log('Newsletter subscription:', email);
        });
    }

    // Load More Articles
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            // Simulate loading more articles
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';

            setTimeout(() => {
                this.innerHTML = 'Load More Articles <i class="fas fa-chevron-down ms-2"></i>';
                alert('More articles loaded! (This would fetch additional articles from the database)');
            }, 1500);
        });
    }

    // Media Center Filters
    const mediaButtons = document.querySelectorAll('.media-btn');
    const mediaItems = document.querySelectorAll('.media-item');

    if (mediaButtons.length > 0) {
        mediaButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                mediaButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filterValue = this.getAttribute('data-media');

                // Show/hide media items based on filter
                mediaItems.forEach(item => {
                    if (filterValue === 'all') {
                        item.classList.remove('hidden');
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        const categories = item.getAttribute('data-category').split(' ');
                        if (categories.includes(filterValue)) {
                            item.classList.remove('hidden');
                            setTimeout(() => {
                                item.style.opacity = '1';
                                item.style.transform = 'translateY(0)';
                            }, 100);
                        } else {
                            item.style.opacity = '0';
                            item.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                item.classList.add('hidden');
                            }, 300);
                        }
                    }
                });
            });
        });
    }

    // Load More Media
    const loadMoreMedia = document.getElementById('loadMoreMedia');
    if (loadMoreMedia) {
        loadMoreMedia.addEventListener('click', function() {
            // Simulate loading more media
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';

            setTimeout(() => {
                this.innerHTML = 'Load More Media <i class="fas fa-chevron-down ms-2"></i>';
                alert('More media items loaded! (This would fetch additional media from the database)');
            }, 1500);
        });
    }

    // Video Modal Handling
    const videoModal = document.getElementById('videoModal');
    if (videoModal) {
        videoModal.addEventListener('hidden.bs.modal', function () {
            // Stop video when modal is closed
            const iframe = this.querySelector('iframe');
            if (iframe) {
                const src = iframe.src;
                iframe.src = '';
                iframe.src = src;
            }
        });
    }

    // Project Details Hero Slider
    let currentSlide = 0;
    const slides = document.querySelectorAll('.project-slide');
    const indicators = document.querySelectorAll('.indicator');

    if (slides.length > 0) {
        // Auto slide every 5 seconds
        setInterval(() => {
            changeSlide(1);
        }, 5000);
    }

    // Floor Plan Tabs
    const planTabs = document.querySelectorAll('.plan-tab');
    const planDetails = document.querySelectorAll('.plan-details');

    planTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs and details
            planTabs.forEach(t => t.classList.remove('active'));
            planDetails.forEach(d => d.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding plan details
            const planType = this.getAttribute('data-plan');
            const targetDetail = document.getElementById(planType);
            if (targetDetail) {
                targetDetail.classList.add('active');
            }
        });
    });

    // Smooth scrolling for project navigation (only for project detail page navigation)
    const projectNavItems = document.querySelectorAll('.project-navigation .nav-item');
    projectNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Only prevent default for project navigation items with anchor links
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();

                // Remove active class from all nav items
                projectNavItems.forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to target section
                const targetSection = document.querySelector(href);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });

    // Update active nav item on scroll
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.project-section');
        const scrollPos = window.scrollY + 200;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navItems.forEach(nav => nav.classList.remove('active'));
                const activeNav = document.querySelector(`.nav-item[href="#${sectionId}"]`);
                if (activeNav) {
                    activeNav.classList.add('active');
                }
            }
        });
    });

    // Blog Details - Newsletter Subscription
    const sidebarNewsletterForm = document.getElementById('sidebarNewsletterForm');
    if (sidebarNewsletterForm) {
        sidebarNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value;

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Show success message
            alert('Thank you for subscribing! You will receive our latest insights.');

            // Reset form
            emailInput.value = '';
        });
    }

    // Comment Form Handling
    const commentForm = document.getElementById('commentForm');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const name = formData.get('name') || this.querySelector('input[placeholder="Your Name"]').value;
            const email = formData.get('email') || this.querySelector('input[placeholder="Your Email"]').value;
            const comment = formData.get('comment') || this.querySelector('textarea').value;

            // Basic validation
            if (!name || !email || !comment) {
                alert('Please fill in all fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }

            // Show success message
            alert('Thank you for your comment! It will be reviewed and published soon.');

            // Reset form
            this.reset();
        });
    }

    // Services Page - Smooth Scrolling
    const smoothScrollLinks = document.querySelectorAll('.smooth-scroll');
    smoothScrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Services Page - Testimonial Carousel
    let currentTestimonial = 0;
    const testimonialSlides = document.querySelectorAll('.testimonial-slide');
    const testimonialIndicators = document.querySelectorAll('.testimonial-indicators .indicator');

    if (testimonialSlides.length > 0) {
        // Auto-rotate testimonials every 5 seconds
        setInterval(() => {
            showTestimonial((currentTestimonial + 1) % testimonialSlides.length);
        }, 5000);
    }
});

// Global function for testimonial carousel
function showTestimonial(index) {
    const testimonialSlides = document.querySelectorAll('.testimonial-slide');
    const testimonialIndicators = document.querySelectorAll('.testimonial-indicators .indicator');

    if (testimonialSlides.length === 0) return;

    // Hide all slides
    testimonialSlides.forEach(slide => {
        slide.classList.remove('active');
    });

    // Remove active from all indicators
    testimonialIndicators.forEach(indicator => {
        indicator.classList.remove('active');
    });

    // Show current slide
    testimonialSlides[index].classList.add('active');
    testimonialIndicators[index].classList.add('active');

    currentTestimonial = index;
}

// Global functions for project hero slider
function changeSlide(direction) {
    const slides = document.querySelectorAll('.project-slide');
    const indicators = document.querySelectorAll('.indicator');

    if (slides.length === 0) return;

    // Remove active class from current slide and indicator
    slides[currentSlide].classList.remove('active');
    indicators[currentSlide].classList.remove('active');

    // Calculate new slide index
    currentSlide += direction;

    if (currentSlide >= slides.length) {
        currentSlide = 0;
    } else if (currentSlide < 0) {
        currentSlide = slides.length - 1;
    }

    // Add active class to new slide and indicator
    slides[currentSlide].classList.add('active');
    indicators[currentSlide].classList.add('active');
}

function currentSlide(n) {
    const slides = document.querySelectorAll('.project-slide');
    const indicators = document.querySelectorAll('.indicator');

    if (slides.length === 0) return;

    // Remove active class from current slide and indicator
    slides[currentSlide].classList.remove('active');
    indicators[currentSlide].classList.remove('active');

    // Set new slide index
    currentSlide = n - 1;

    // Add active class to new slide and indicator
    slides[currentSlide].classList.add('active');
    indicators[currentSlide].classList.add('active');
}
