@extends('admin.layouts.app')

@section('title', 'Create New Project')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Create New Project</h1>
                <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Projects
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('admin.projects.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Project Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">URL Slug</label>
                                    <input type="text" class="form-control" id="slug" name="slug" 
                                           value="{{ old('slug') }}" placeholder="Auto-generated from name">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="developer" class="form-label">Developer <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="developer" name="developer" 
                                           value="{{ old('developer') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_type" class="form-label">Project Type <span class="text-danger">*</span></label>
                                    <select class="form-control" id="project_type" name="project_type" required>
                                        <option value="">Select Project Type</option>
                                        <option value="Residential" {{ old('project_type') == 'Residential' ? 'selected' : '' }}>Residential</option>
                                        <option value="Commercial" {{ old('project_type') == 'Commercial' ? 'selected' : '' }}>Commercial</option>
                                        <option value="Mixed Use" {{ old('project_type') == 'Mixed Use' ? 'selected' : '' }}>Mixed Use</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description</label>
                            <textarea class="form-control" id="short_description" name="short_description" 
                                      rows="3">{{ old('short_description') }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="6">{{ old('description') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Location Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Location Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           value="{{ old('location') }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="{{ old('city') }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="state" name="state" 
                                           value="{{ old('state', 'Haryana') }}" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Project Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="property_types" class="form-label">Property Types <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="property_types" name="property_types" 
                                           value="{{ old('property_types') }}" placeholder="e.g., 1BHK, 2BHK, 3BHK" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="upcoming" {{ old('status') == 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                                        <option value="ongoing" {{ old('status') == 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                                        <option value="ready_to_move" {{ old('status') == 'ready_to_move' ? 'selected' : '' }}>Ready to Move</option>
                                        <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="starting_price" class="form-label">Starting Price (₹) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="starting_price" name="starting_price" 
                                           value="{{ old('starting_price') }}" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price_range" class="form-label">Price Range <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="price_range" name="price_range" 
                                           value="{{ old('price_range') }}" placeholder="e.g., ₹1.25Cr - ₹3.5Cr" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="possession_date" class="form-label">Possession Date <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="possession_date" name="possession_date" 
                                           value="{{ old('possession_date') }}" placeholder="e.g., Dec 2025" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="total_area" class="form-label">Total Area (sq ft)</label>
                                    <input type="number" class="form-control" id="total_area" name="total_area" 
                                           value="{{ old('total_area') }}" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="total_units" class="form-label">Total Units</label>
                                    <input type="number" class="form-control" id="total_units" name="total_units" 
                                           value="{{ old('total_units') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="rera_id" class="form-label">RERA ID</label>
                                    <input type="text" class="form-control" id="rera_id" name="rera_id" 
                                           value="{{ old('rera_id') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images & Media -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Images & Media (Multiple Photos Supported)</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="images" class="form-label">Project Images</label>
                                    <input type="file" class="form-control" id="images" name="images[]" 
                                           multiple accept="image/*">
                                    <small class="form-text text-muted">Select multiple images for the project gallery</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="floor_plans" class="form-label">Floor Plans</label>
                                    <input type="file" class="form-control" id="floor_plans" name="floor_plans[]" 
                                           multiple accept="image/*,.pdf">
                                    <small class="form-text text-muted">Upload floor plan images or PDFs</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features & Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Features & Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="featured" name="featured" 
                                           value="1" {{ old('featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="featured">Featured Project</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="feature_slider" name="feature_slider" 
                                           value="1" {{ old('feature_slider') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="feature_slider">Show in Slider</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">Active</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="{{ old('sort_order', 0) }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                   value="{{ old('meta_title') }}">
                        </div>
                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control" id="meta_description" name="meta_description" 
                                      rows="3">{{ old('meta_description') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});
</script>
@endsection
