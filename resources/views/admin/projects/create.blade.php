@extends('admin.layouts.app')

@section('title', 'Create Project')

@section('content')
<div class="d-flex flex-wrap justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h4 mb-1">Create New Project</h1>
        <p class="text-muted mb-0">Add a new real estate project to your portfolio</p>
    </div>
    <a href="{{ route('admin.projects.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Projects
    </a>
</div>
<form method="POST" action="{{ route('admin.projects.store') }}" enctype="multipart/form-data">
    @csrf
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Project Information</h5>
                </div>
                <div class="card-body">
                    <!-- Project Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Project Name *</label>
                        <input type="text" 
                               name="name" 
                               id="name"
                               class="form-control @error('name') is-invalid @enderror" 
                               value="{{ old('name') }}" 
                               required
                               placeholder="Enter project name">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Developer -->
                    <div class="mb-3">
                        <label for="developer" class="form-label">Developer *</label>
                        <input type="text" 
                               name="developer" 
                               id="developer"
                               class="form-control @error('developer') is-invalid @enderror" 
                               value="{{ old('developer') }}" 
                               required
                               placeholder="Enter developer name">
                        @error('developer')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Location Details -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location" class="form-label">Location *</label>
                                <input type="text" 
                                       name="location" 
                                       id="location"
                                       class="form-control @error('location') is-invalid @enderror" 
                                       value="{{ old('location') }}" 
                                       required
                                       placeholder="Enter location">
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="city" class="form-label">City *</label>
                                <input type="text" 
                                       name="city" 
                                       id="city"
                                       class="form-control @error('city') is-invalid @enderror" 
                                       value="{{ old('city') }}" 
                                       required
                                       placeholder="Enter city">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="state" class="form-label">State *</label>
                                <input type="text" 
                                       name="state" 
                                       id="state"
                                       class="form-control @error('state') is-invalid @enderror" 
                                       value="{{ old('state', 'Haryana') }}" 
                                       required
                                       placeholder="Enter state">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Project Type and Property Types -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="project_type" class="form-label">Project Type *</label>
                                <select name="project_type" 
                                        id="project_type"
                                        class="form-control @error('project_type') is-invalid @enderror" 
                                        required>
                                    <option value="">Select Project Type</option>
                                    <option value="Residential" {{ old('project_type') === 'Residential' ? 'selected' : '' }}>Residential</option>
                                    <option value="Commercial" {{ old('project_type') === 'Commercial' ? 'selected' : '' }}>Commercial</option>
                                    <option value="Mixed Use" {{ old('project_type') === 'Mixed Use' ? 'selected' : '' }}>Mixed Use</option>
                                    <option value="Plotted Development" {{ old('project_type') === 'Plotted Development' ? 'selected' : '' }}>Plotted Development</option>
                                </select>
                                @error('project_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="property_types" class="form-label">Property Types *</label>
                                <input type="text" 
                                       name="property_types" 
                                       id="property_types"
                                       class="form-control @error('property_types') is-invalid @enderror" 
                                       value="{{ old('property_types') }}" 
                                       required
                                       placeholder="e.g., 1BHK, 2BHK, 3BHK">
                                @error('property_types')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Short Description -->
                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea name="short_description" 
                                  id="short_description"
                                  class="form-control @error('short_description') is-invalid @enderror" 
                                  rows="3"
                                  placeholder="Brief project description (max 500 characters)">{{ old('short_description') }}</textarea>
                        @error('short_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Full Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Full Description</label>
                        <textarea name="description" 
                                  id="description"
                                  class="form-control @error('description') is-invalid @enderror" 
                                  rows="6"
                                  placeholder="Detailed project description">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-rupee-sign me-2"></i> Pricing Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="starting_price" class="form-label">Starting Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" 
                                           name="starting_price" 
                                           id="starting_price"
                                           class="form-control @error('starting_price') is-invalid @enderror" 
                                           value="{{ old('starting_price') }}" 
                                           step="0.01"
                                           min="0"
                                           required
                                           placeholder="0.00">
                                </div>
                                @error('starting_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price_range" class="form-label">Price Range *</label>
                                <input type="text" 
                                       name="price_range" 
                                       id="price_range"
                                       class="form-control @error('price_range') is-invalid @enderror" 
                                       value="{{ old('price_range') }}" 
                                       required
                                       placeholder="e.g., ₹50L - ₹1.2Cr">
                                @error('price_range')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="possession_date" class="form-label">Possession Date *</label>
                        <input type="text" 
                               name="possession_date" 
                               id="possession_date"
                               class="form-control @error('possession_date') is-invalid @enderror" 
                               value="{{ old('possession_date') }}" 
                               required
                               placeholder="e.g., Dec 2025, Ready to Move">
                        @error('possession_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Project Specifications -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i> Project Specifications</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="total_area" class="form-label">Total Area (sq ft)</label>
                                <input type="number"
                                       name="total_area"
                                       id="total_area"
                                       class="form-control @error('total_area') is-invalid @enderror"
                                       value="{{ old('total_area') }}"
                                       step="0.01"
                                       min="0"
                                       placeholder="e.g., 250000">
                                @error('total_area')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Total project area in square feet</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="total_units" class="form-label">Total Units</label>
                                <input type="number"
                                       name="total_units"
                                       id="total_units"
                                       class="form-control @error('total_units') is-invalid @enderror"
                                       value="{{ old('total_units') }}"
                                       min="0"
                                       placeholder="e.g., 450">
                                @error('total_units')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Total number of units/apartments</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="rera_id" class="form-label">RERA ID</label>
                                <input type="text"
                                       name="rera_id"
                                       id="rera_id"
                                       class="form-control @error('rera_id') is-invalid @enderror"
                                       value="{{ old('rera_id') }}"
                                       placeholder="e.g., RERA-GGM-567-2023">
                                @error('rera_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">RERA registration number</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Features & Amenities -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i> Project Features & Amenities</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="amenities" class="form-label">Amenities</label>
                        <textarea name="amenities"
                                  id="amenities"
                                  class="form-control @error('amenities') is-invalid @enderror"
                                  rows="4"
                                  placeholder="List key amenities (one per line):&#10;Swimming Pool&#10;Fitness Center&#10;Landscaped Gardens&#10;Kids Play Area&#10;24/7 Security&#10;Covered Parking">{{ old('amenities') }}</textarea>
                        @error('amenities')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Enter amenities one per line</small>
                    </div>

                    <div class="form-group">
                        <label for="specifications" class="form-label">Project Specifications</label>
                        <textarea name="specifications"
                                  id="specifications"
                                  class="form-control @error('specifications') is-invalid @enderror"
                                  rows="4"
                                  placeholder="Enter project specifications:&#10;Towers: 6 Towers&#10;Floors: G+18&#10;Parking: Covered parking for all units&#10;Elevation: Modern architecture">{{ old('specifications') }}</textarea>
                        @error('specifications')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Enter specifications one per line</small>
                    </div>

                    <div class="form-group">
                        <label for="floor_plans" class="form-label">Floor Plan Information</label>
                        <textarea name="floor_plans"
                                  id="floor_plans"
                                  class="form-control @error('floor_plans') is-invalid @enderror"
                                  rows="4"
                                  placeholder="Enter floor plan details:&#10;1 BHK: 485-520 sq.ft, ₹1.62-1.85 Cr&#10;2 BHK: 785-850 sq.ft, ₹2.45-2.85 Cr&#10;3 BHK: 1185-1285 sq.ft, ₹3.85-4.45 Cr">{{ old('floor_plans') }}</textarea>
                        @error('floor_plans')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Enter floor plan details for different configurations</small>
                    </div>
                </div>
            </div>

            <!-- SEO Section -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i> SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" 
                               name="meta_title" 
                               id="meta_title"
                               class="form-control @error('meta_title') is-invalid @enderror" 
                               value="{{ old('meta_title') }}" 
                               placeholder="SEO title for search engines">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea name="meta_description" 
                                  id="meta_description"
                                  class="form-control @error('meta_description') is-invalid @enderror" 
                                  rows="3"
                                  placeholder="SEO description for search engines">{{ old('meta_description') }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> Actions</h5>
                </div>
                <div class="card-body d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Create Project
                    </button>
                    <a href="{{ route('admin.projects.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection
