@extends('admin.layouts.app')

@section('title', 'Projects Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Projects Management</h1>
                <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Project
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">All Projects</h6>
                </div>
                <div class="card-body">
                    @if($projects->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Project</th>
                                        <th>Location</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Features</th>
                                        <th>Images</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($projects as $project)
                                        <tr>
                                            <td>
                                                <strong>{{ $project->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $project->developer }}</small>
                                                <br>
                                                <code>{{ $project->slug }}</code>
                                            </td>
                                            <td>
                                                {{ $project->location }}
                                                <br>
                                                <small class="text-muted">{{ $project->city }}, {{ $project->state }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ $project->formatted_starting_price }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $project->price_range }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $project->status == 'ready_to_move' ? 'success' : ($project->status == 'ongoing' ? 'warning' : 'info') }}">
                                                    {{ ucwords(str_replace('_', ' ', $project->status)) }}
                                                </span>
                                                <br>
                                                @if($project->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($project->featured)
                                                    <span class="badge bg-warning">Featured</span>
                                                @endif
                                                @if($project->feature_slider)
                                                    <span class="badge bg-info">Slider</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($project->images && count($project->images) > 0)
                                                    <span class="badge bg-primary">{{ count($project->images) }} Images</span>
                                                @else
                                                    <span class="text-muted">No Images</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.projects.show', $project) }}" 
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.projects.edit', $project) }}" 
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{{ $project->project_url }}" target="_blank"
                                                       class="btn btn-sm btn-success" title="View on Site">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <form action="{{ route('admin.projects.destroy', $project) }}" 
                                                          method="POST" class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this project?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No projects found</h5>
                            <p class="text-muted">Create your first project to get started.</p>
                            <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Project
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
