@extends('admin.layouts.app')

@section('title', 'Edit Project')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
        <span class="breadcrumb-separator">/</span>
        <a href="{{ route('admin.projects.index') }}" class="breadcrumb-item">Projects</a>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-item active">Edit {{ $project->name }}</span>
    </div>
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Edit Project</h1>
            <p class="page-subtitle">Update project information and details</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ $project->project_url }}" class="btn-admin btn-admin-success" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>View on Website
            </a>
            <a href="{{ route('admin.projects.show', $project) }}" class="btn-admin btn-admin-outline">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline">
                <i class="fas fa-arrow-left me-2"></i>Back to Projects
            </a>
        </div>
    </div>
@endsection

@section('content')
    <form method="POST" action="{{ route('admin.projects.update', $project) }}" data-auto-save="edit-project">
        @csrf
        @method('PUT')
        
        <div class="row">
            <div class="col-lg-8">
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Project Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Project Name -->
                        <div class="form-group">
                            <label for="name" class="form-label">Project Name *</label>
                            <input type="text" 
                                   name="name" 
                                   id="name"
                                   class="form-control @error('name') is-invalid @enderror" 
                                   value="{{ old('name', $project->name) }}" 
                                   required
                                   placeholder="Enter project name">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Developer -->
                        <div class="form-group">
                            <label for="developer" class="form-label">Developer *</label>
                            <input type="text" 
                                   name="developer" 
                                   id="developer"
                                   class="form-control @error('developer') is-invalid @enderror" 
                                   value="{{ old('developer', $project->developer) }}" 
                                   required
                                   placeholder="Enter developer name">
                            @error('developer')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Location Details -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Location *</label>
                                    <input type="text" 
                                           name="location" 
                                           id="location"
                                           class="form-control @error('location') is-invalid @enderror" 
                                           value="{{ old('location', $project->location) }}" 
                                           required
                                           placeholder="Enter location">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="city" class="form-label">City *</label>
                                    <input type="text" 
                                           name="city" 
                                           id="city"
                                           class="form-control @error('city') is-invalid @enderror" 
                                           value="{{ old('city', $project->city) }}" 
                                           required
                                           placeholder="Enter city">
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="state" class="form-label">State *</label>
                                    <input type="text" 
                                           name="state" 
                                           id="state"
                                           class="form-control @error('state') is-invalid @enderror" 
                                           value="{{ old('state', $project->state) }}" 
                                           required
                                           placeholder="Enter state">
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Project Type and Property Types -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="project_type" class="form-label">Project Type *</label>
                                    <select name="project_type" 
                                            id="project_type"
                                            class="form-control @error('project_type') is-invalid @enderror" 
                                            required>
                                        <option value="">Select Project Type</option>
                                        <option value="Residential" {{ old('project_type', $project->project_type) === 'Residential' ? 'selected' : '' }}>Residential</option>
                                        <option value="Commercial" {{ old('project_type', $project->project_type) === 'Commercial' ? 'selected' : '' }}>Commercial</option>
                                        <option value="Mixed Use" {{ old('project_type', $project->project_type) === 'Mixed Use' ? 'selected' : '' }}>Mixed Use</option>
                                        <option value="Plotted Development" {{ old('project_type', $project->project_type) === 'Plotted Development' ? 'selected' : '' }}>Plotted Development</option>
                                    </select>
                                    @error('project_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="property_types" class="form-label">Property Types *</label>
                                    <input type="text" 
                                           name="property_types" 
                                           id="property_types"
                                           class="form-control @error('property_types') is-invalid @enderror" 
                                           value="{{ old('property_types', $project->property_types) }}" 
                                           required
                                           placeholder="e.g., 1BHK, 2BHK, 3BHK">
                                    @error('property_types')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Short Description -->
                        <div class="form-group">
                            <label for="short_description" class="form-label">Short Description</label>
                            <textarea name="short_description" 
                                      id="short_description"
                                      class="form-control @error('short_description') is-invalid @enderror" 
                                      rows="3"
                                      placeholder="Brief project description (max 500 characters)">{{ old('short_description', $project->short_description) }}</textarea>
                            @error('short_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Full Description -->
                        <div class="form-group">
                            <label for="description" class="form-label">Full Description</label>
                            <textarea name="description" 
                                      id="description"
                                      class="form-control @error('description') is-invalid @enderror" 
                                      rows="6"
                                      placeholder="Detailed project description">{{ old('description', $project->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-rupee-sign me-2"></i>
                            Pricing Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="starting_price" class="form-label">Starting Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₹</span>
                                        <input type="number" 
                                               name="starting_price" 
                                               id="starting_price"
                                               class="form-control @error('starting_price') is-invalid @enderror" 
                                               value="{{ old('starting_price', $project->starting_price) }}" 
                                               step="0.01"
                                               min="0"
                                               required
                                               placeholder="0.00">
                                    </div>
                                    @error('starting_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price_range" class="form-label">Price Range *</label>
                                    <input type="text" 
                                           name="price_range" 
                                           id="price_range"
                                           class="form-control @error('price_range') is-invalid @enderror" 
                                           value="{{ old('price_range', $project->price_range) }}" 
                                           required
                                           placeholder="e.g., ₹50L - ₹1.2Cr">
                                    @error('price_range')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="possession_date" class="form-label">Possession Date *</label>
                            <input type="text" 
                                   name="possession_date" 
                                   id="possession_date"
                                   class="form-control @error('possession_date') is-invalid @enderror" 
                                   value="{{ old('possession_date', $project->possession_date) }}" 
                                   required
                                   placeholder="e.g., Dec 2025, Ready to Move">
                            @error('possession_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Project Specifications -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            Project Specifications
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="total_area" class="form-label">Total Area (sq ft)</label>
                                    <input type="number" 
                                           name="total_area" 
                                           id="total_area"
                                           class="form-control @error('total_area') is-invalid @enderror" 
                                           value="{{ old('total_area', $project->total_area) }}" 
                                           step="0.01"
                                           min="0"
                                           placeholder="e.g., 250000">
                                    @error('total_area')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="total_units" class="form-label">Total Units</label>
                                    <input type="number" 
                                           name="total_units" 
                                           id="total_units"
                                           class="form-control @error('total_units') is-invalid @enderror" 
                                           value="{{ old('total_units', $project->total_units) }}" 
                                           min="0"
                                           placeholder="e.g., 450">
                                    @error('total_units')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="rera_id" class="form-label">RERA ID</label>
                                    <input type="text" 
                                           name="rera_id" 
                                           id="rera_id"
                                           class="form-control @error('rera_id') is-invalid @enderror" 
                                           value="{{ old('rera_id', $project->rera_id) }}" 
                                           placeholder="e.g., RERA-GGM-567-2023">
                                    @error('rera_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Section -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            SEO Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" 
                                   name="meta_title" 
                                   id="meta_title"
                                   class="form-control @error('meta_title') is-invalid @enderror" 
                                   value="{{ old('meta_title', $project->meta_title) }}" 
                                   placeholder="SEO title for search engines">
                            @error('meta_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea name="meta_description" 
                                      id="meta_description"
                                      class="form-control @error('meta_description') is-invalid @enderror" 
                                      rows="3"
                                      placeholder="SEO description for search engines">{{ old('meta_description', $project->meta_description) }}</textarea>
                            @error('meta_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Publish Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="status" class="form-label">Project Status *</label>
                            <select name="status" 
                                    id="status"
                                    class="form-control @error('status') is-invalid @enderror" 
                                    required>
                                <option value="upcoming" {{ old('status', $project->status) === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                                <option value="ongoing" {{ old('status', $project->status) === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                                <option value="ready_to_move" {{ old('status', $project->status) === 'ready_to_move' ? 'selected' : '' }}>Ready to Move</option>
                                <option value="completed" {{ old('status', $project->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input"
                                       type="checkbox"
                                       name="featured"
                                       id="featured"
                                       value="1"
                                       {{ old('featured', $project->featured) ? 'checked' : '' }}>
                                <label class="form-check-label" for="featured">
                                    Featured Project
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input"
                                       type="checkbox"
                                       name="feature_slider"
                                       id="feature_slider"
                                       value="1"
                                       {{ old('feature_slider', $project->feature_slider) ? 'checked' : '' }}>
                                <label class="form-check-label" for="feature_slider">
                                    Show in Feature Slider
                                </label>
                            </div>
                            <small class="form-text text-muted">Include this project in the featured projects slider</small>
                        </div>

                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number"
                                   class="form-control @error('sort_order') is-invalid @enderror"
                                   id="sort_order"
                                   name="sort_order"
                                   value="{{ old('sort_order', $project->sort_order ?? 0) }}"
                                   min="0">
                            <small class="form-text text-muted">Lower numbers appear first in the feature slider</small>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       name="is_active" 
                                       id="is_active"
                                       value="1"
                                       {{ old('is_active', $project->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Project
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn-admin btn-admin-primary">
                                <i class="fas fa-save me-2"></i>Update Project
                            </button>
                            <a href="{{ route('admin.projects.show', $project) }}" class="btn-admin btn-admin-outline">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Info -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info me-2"></i>
                            Project Info
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Project ID</small>
                            <div><strong>#{{ $project->id }}</strong></div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Current Slug</small>
                            <div><code>{{ $project->slug }}</code></div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Created</small>
                            <div><strong>{{ $project->created_at->format('M d, Y H:i') }}</strong></div>
                        </div>
                        <div>
                            <small class="text-muted">Last Updated</small>
                            <div><strong>{{ $project->updated_at->format('M d, Y H:i') }}</strong></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection
