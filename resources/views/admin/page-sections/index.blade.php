@extends('admin.layouts.app')

@section('title', 'Manage Sections - ' . $page->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-puzzle-piece"></i> Manage Sections - {{ $page->name }}
        </h1>
        <div>
            <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Pages
            </a>
            <a href="{{ route('admin.pages.sections.create', $page) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add New Section
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Sections List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Page Sections</h6>
        </div>
        <div class="card-body">
            @if($sections->count() > 0)
                <div id="sections-container">
                    @foreach($sections as $section)
                    <div class="card mb-3 section-item" data-id="{{ $section->id }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-grip-vertical text-muted me-2 drag-handle" style="cursor: move;"></i>
                                <h6 class="mb-0">
                                    <span class="badge bg-info me-2">{{ $sectionTypes[$section->section_type] ?? $section->section_type }}</span>
                                    {{ $section->title ?: 'Untitled Section' }}
                                </h6>
                                @if(!$section->is_active)
                                    <span class="badge bg-warning ms-2">Inactive</span>
                                @endif
                            </div>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.pages.sections.edit', [$page, $section]) }}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteSection({{ $section->id }})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    @if($section->subtitle)
                                        <p class="text-muted mb-2">{{ $section->subtitle }}</p>
                                    @endif
                                    @if($section->content)
                                        <div class="content-preview">
                                            {!! \Str::limit(strip_tags($section->content), 200) !!}
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-4">
                                    @if($section->images && count($section->images) > 0)
                                        <div class="section-images">
                                            <small class="text-muted">Images ({{ count($section->images) }}):</small>
                                            <div class="d-flex flex-wrap mt-1">
                                                @foreach(array_slice($section->images, 0, 3) as $image)
                                                    <img src="{{ asset('storage/' . $image) }}" 
                                                         class="img-thumbnail me-1 mb-1" 
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                @endforeach
                                                @if(count($section->images) > 3)
                                                    <div class="img-thumbnail d-flex align-items-center justify-content-center me-1 mb-1" 
                                                         style="width: 50px; height: 50px; background: #f8f9fa;">
                                                        <small>+{{ count($section->images) - 3 }}</small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No sections found</h5>
                    <p class="text-muted">Start building your page by adding sections.</p>
                    <a href="{{ route('admin.pages.sections.create', $page) }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Section
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this section? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Initialize sortable
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('sections-container');
    if (container) {
        new Sortable(container, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                updateSectionOrder();
            }
        });
    }
});

// Update section order
function updateSectionOrder() {
    const sections = [];
    document.querySelectorAll('.section-item').forEach((item, index) => {
        sections.push({
            id: parseInt(item.dataset.id),
            sort_order: index + 1
        });
    });

    fetch(`{{ route('admin.pages.sections.update-order', $page) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ sections: sections })
    });
}

// Delete section
function deleteSection(sectionId) {
    const form = document.getElementById('deleteForm');
    form.action = `{{ route('admin.pages.sections.index', $page) }}/${sectionId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endsection
