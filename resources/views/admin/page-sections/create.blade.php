@extends('admin.layouts.app')

@section('title', 'Add New Section - ' . $page->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus"></i> Add New Section - {{ $page->name }}
        </h1>
        <a href="{{ route('admin.pages.sections.index', $page) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Sections
        </a>
    </div>

    <!-- Create Section Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Section Details</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.pages.sections.store', $page) }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="section_type" class="form-label">Section Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('section_type') is-invalid @enderror"
                                    id="section_type" name="section_type" required onchange="toggleSectionFields()">
                                <option value="">Select Section Type</option>
                                @foreach($sectionTypes as $key => $label)
                                    <option value="{{ $key }}" {{ (old('section_type') ?? request('type')) == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('section_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Lower numbers appear first</small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="title" class="form-label">Section Title</label>
                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                           id="title" name="title" value="{{ old('title') }}" maxlength="255">
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="subtitle" class="form-label">Section Subtitle</label>
                    <textarea class="form-control @error('subtitle') is-invalid @enderror" 
                              id="subtitle" name="subtitle" rows="2">{{ old('subtitle') }}</textarea>
                    @error('subtitle')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="content" class="form-label">Section Content</label>
                    <textarea class="form-control @error('content') is-invalid @enderror"
                              id="content" name="content" rows="8">{{ old('content') }}</textarea>
                    @error('content')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Icon and Button Fields -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="icon" class="form-label">Section Icon</label>
                            <div class="input-group">
                                <select class="form-select @error('icon_type') is-invalid @enderror"
                                        id="icon_type" name="icon_type" style="max-width: 100px;">
                                    <option value="fas" {{ old('icon_type', 'fas') == 'fas' ? 'selected' : '' }}>fas</option>
                                    <option value="far" {{ old('icon_type') == 'far' ? 'selected' : '' }}>far</option>
                                    <option value="fab" {{ old('icon_type') == 'fab' ? 'selected' : '' }}>fab</option>
                                </select>
                                <input type="text" class="form-control @error('icon') is-invalid @enderror"
                                       id="icon" name="icon" value="{{ old('icon') }}"
                                       placeholder="e.g., fa-home, fa-star, fa-phone">
                                <button type="button" class="btn btn-outline-secondary" id="icon-preview-btn">
                                    <i id="icon-preview" class="{{ old('icon_type', 'fas') }} {{ old('icon') ? old('icon') : 'fa-question' }}"></i>
                                </button>
                            </div>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                Enter FontAwesome icon class (without fa- prefix). Example: home, star, phone
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="button_text" class="form-label">Button Text</label>
                            <input type="text" class="form-control @error('button_text') is-invalid @enderror"
                                   id="button_text" name="button_text" value="{{ old('button_text') }}"
                                   placeholder="e.g., Learn More, Contact Us">
                            @error('button_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="button_url" class="form-label">Button URL</label>
                    <input type="url" class="form-control @error('button_url') is-invalid @enderror"
                           id="button_url" name="button_url" value="{{ old('button_url') }}"
                           placeholder="https://example.com or /contact">
                    @error('button_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">
                        Enter full URL (https://...) or relative path (/contact)
                    </small>
                </div>

                <div class="mb-3">
                    <label for="images" class="form-label">Section Images</label>
                    <input type="file" class="form-control @error('images.*') is-invalid @enderror" 
                           id="images" name="images[]" multiple accept="image/*">
                    @error('images.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">
                        You can select multiple images. Supported formats: JPEG, PNG, JPG, GIF, WebP. Max size: 2MB each.
                    </small>
                    <div id="image-preview" class="mt-2"></div>
                </div>

                <!-- Section-Specific Fields -->
                <div id="section-specific-fields">
                    <!-- Hero Section Fields -->
                    <div id="hero-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">Hero Section Settings</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="cta_text" class="form-label">Primary Button Text</label>
                                    <input type="text" class="form-control" id="cta_text" name="cta_text" value="{{ old('cta_text') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="cta_url" class="form-label">Primary Button URL</label>
                                    <input type="url" class="form-control" id="cta_url" name="cta_url" value="{{ old('cta_url') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="secondary_cta_text" class="form-label">Secondary Button Text</label>
                                    <input type="text" class="form-control" id="secondary_cta_text" name="secondary_cta_text" value="{{ old('secondary_cta_text') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="secondary_cta_url" class="form-label">Secondary Button URL</label>
                                    <input type="url" class="form-control" id="secondary_cta_url" name="secondary_cta_url" value="{{ old('secondary_cta_url') }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- About Section Fields -->
                    <div id="about-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">About Section Settings</h5>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_stats" name="show_stats" value="1" {{ old('show_stats') ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_stats">
                                    Show Statistics
                                </label>
                            </div>
                        </div>
                        <div id="stats-container" style="display: none;">
                            <label class="form-label">Statistics</label>
                            <div id="stats-list">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="stats[0][number]" placeholder="Number (e.g., 5+)" value="{{ old('stats.0.number') }}">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="stats[0][label]" placeholder="Label (e.g., Years of Excellence)" value="{{ old('stats.0.label') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeStatItem(this)">Remove</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addStatItem()">Add Statistic</button>
                        </div>
                    </div>

                    <!-- Features Section Fields -->
                    <div id="features-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">Features Section Settings</h5>
                        <label class="form-label">Feature Items</label>
                        <div id="features-list">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][icon]" placeholder="Icon (e.g., fa-check)" value="{{ old('items.0.icon') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="items[0][title]" placeholder="Feature Title" value="{{ old('items.0.title') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="items[0][description]" placeholder="Feature Description" value="{{ old('items.0.description') }}">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFeatureItem(this)">×</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addFeatureItem()">Add Feature</button>
                    </div>

                    <!-- Projects Section Fields -->
                    <div id="projects-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">Projects Section Settings</h5>
                        <label class="form-label">Project Items</label>
                        <div id="projects-list">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][category]" placeholder="Category (e.g., Premium Residential)" value="{{ old('items.0.category') }}">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][title]" placeholder="Project Title" value="{{ old('items.0.title') }}">
                                        </div>
                                        <div class="col-md-3">
                                            <textarea class="form-control" name="items[0][description]" placeholder="Project Description" rows="2">{{ old('items.0.description') }}</textarea>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" class="form-control" name="items[0][url]" placeholder="Project URL" value="{{ old('items.0.url') }}">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeProjectItem(this)">×</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addProjectItem()">Add Project</button>
                    </div>

                    <!-- Locations Section Fields -->
                    <div id="locations-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">Locations Section Settings</h5>
                        <label class="form-label">Location Items</label>
                        <div id="locations-list">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][title]" placeholder="Location Name" value="{{ old('items.0.title') }}">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][description]" placeholder="Projects Count" value="{{ old('items.0.description') }}">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][image]" placeholder="Image Path" value="{{ old('items.0.image') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" class="form-control" name="items[0][url]" placeholder="Location URL" value="{{ old('items.0.url') }}">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLocationItem(this)">×</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addLocationItem()">Add Location</button>
                    </div>

                    <!-- Services Section Fields -->
                    <div id="services-fields" class="section-fields" style="display: none;">
                        <h5 class="mb-3">Services Section Settings</h5>
                        <label class="form-label">Service Items</label>
                        <div id="services-list">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="items[0][title]" placeholder="Service Title" value="{{ old('items.0.title') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <textarea class="form-control" name="items[0][description]" placeholder="Service Description" rows="2">{{ old('items.0.description') }}</textarea>
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" class="form-control" name="items[0][url]" placeholder="Service URL" value="{{ old('items.0.url') }}">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeServiceItem(this)">×</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addServiceItem()">Add Service</button>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            Active (Show on website)
                        </label>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.pages.sections.index', $page) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Section
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- CKEditor -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

<script>
// Initialize CKEditor
ClassicEditor
    .create(document.querySelector('#content'), {
        toolbar: [
            'heading', '|',
            'bold', 'italic', 'link', '|',
            'bulletedList', 'numberedList', '|',
            'outdent', 'indent', '|',
            'blockQuote', 'insertTable', '|',
            'undo', 'redo'
        ]
    })
    .catch(error => {
        console.error(error);
    });

// Image preview functionality
document.getElementById('images').addEventListener('change', function(e) {
    const preview = document.getElementById('image-preview');
    preview.innerHTML = '';
    
    if (e.target.files.length > 0) {
        const previewContainer = document.createElement('div');
        previewContainer.className = 'row';
        
        Array.from(e.target.files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-md-2 col-sm-3 col-4 mb-2';
                    col.innerHTML = `
                        <div class="card">
                            <img src="${e.target.result}" class="card-img-top" style="height: 100px; object-fit: cover;">
                            <div class="card-body p-2">
                                <small class="text-muted">${file.name}</small>
                            </div>
                        </div>
                    `;
                    previewContainer.appendChild(col);
                };
                reader.readAsDataURL(file);
            }
        });
        
        preview.appendChild(previewContainer);
    }
});

// Icon preview functionality
function updateIconPreview() {
    const iconType = document.getElementById('icon_type').value;
    const iconName = document.getElementById('icon').value;
    const preview = document.getElementById('icon-preview');

    if (iconName) {
        const iconClass = iconName.startsWith('fa-') ? iconName : 'fa-' + iconName;
        preview.className = `${iconType} ${iconClass}`;
    } else {
        preview.className = `${iconType} fa-question`;
    }
}

document.getElementById('icon_type').addEventListener('change', updateIconPreview);
document.getElementById('icon').addEventListener('input', updateIconPreview);

// Initialize icon preview
updateIconPreview();

// Section-specific field toggling
function toggleSectionFields() {
    const sectionType = document.getElementById('section_type').value;
    const allFields = document.querySelectorAll('.section-fields');

    // Hide all section-specific fields
    allFields.forEach(field => field.style.display = 'none');

    // Show relevant fields based on section type
    if (sectionType) {
        const targetFields = document.getElementById(sectionType + '-fields');
        if (targetFields) {
            targetFields.style.display = 'block';
        }
    }
}

// Stats management functions
document.getElementById('show_stats').addEventListener('change', function() {
    const statsContainer = document.getElementById('stats-container');
    statsContainer.style.display = this.checked ? 'block' : 'none';
});

function addStatItem() {
    const statsList = document.getElementById('stats-list');
    const index = statsList.children.length;
    const newStat = document.createElement('div');
    newStat.className = 'row mb-2';
    newStat.innerHTML = `
        <div class="col-md-4">
            <input type="text" class="form-control" name="stats[${index}][number]" placeholder="Number (e.g., 5+)">
        </div>
        <div class="col-md-6">
            <input type="text" class="form-control" name="stats[${index}][label]" placeholder="Label (e.g., Years of Excellence)">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeStatItem(this)">Remove</button>
        </div>
    `;
    statsList.appendChild(newStat);
}

function removeStatItem(button) {
    button.closest('.row').remove();
}

// Features management functions
function addFeatureItem() {
    const featuresList = document.getElementById('features-list');
    const index = featuresList.children.length;
    const newFeature = document.createElement('div');
    newFeature.className = 'card mb-3';
    newFeature.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][icon]" placeholder="Icon (e.g., fa-check)">
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="items[${index}][title]" placeholder="Feature Title">
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="items[${index}][description]" placeholder="Feature Description">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFeatureItem(this)">×</button>
                </div>
            </div>
        </div>
    `;
    featuresList.appendChild(newFeature);
}

function removeFeatureItem(button) {
    button.closest('.card').remove();
}

// Projects management functions
function addProjectItem() {
    const projectsList = document.getElementById('projects-list');
    const index = projectsList.children.length;
    const newProject = document.createElement('div');
    newProject.className = 'card mb-3';
    newProject.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][category]" placeholder="Category (e.g., Premium Residential)">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][title]" placeholder="Project Title">
                </div>
                <div class="col-md-3">
                    <textarea class="form-control" name="items[${index}][description]" placeholder="Project Description" rows="2"></textarea>
                </div>
                <div class="col-md-2">
                    <input type="text" class="form-control" name="items[${index}][url]" placeholder="Project URL">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeProjectItem(this)">×</button>
                </div>
            </div>
        </div>
    `;
    projectsList.appendChild(newProject);
}

function removeProjectItem(button) {
    button.closest('.card').remove();
}

// Locations management functions
function addLocationItem() {
    const locationsList = document.getElementById('locations-list');
    const index = locationsList.children.length;
    const newLocation = document.createElement('div');
    newLocation.className = 'card mb-3';
    newLocation.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][title]" placeholder="Location Name">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][description]" placeholder="Projects Count">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][image]" placeholder="Image Path">
                </div>
                <div class="col-md-2">
                    <input type="text" class="form-control" name="items[${index}][url]" placeholder="Location URL">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLocationItem(this)">×</button>
                </div>
            </div>
        </div>
    `;
    locationsList.appendChild(newLocation);
}

function removeLocationItem(button) {
    button.closest('.card').remove();
}

// Services management functions
function addServiceItem() {
    const servicesList = document.getElementById('services-list');
    const index = servicesList.children.length;
    const newService = document.createElement('div');
    newService.className = 'card mb-3';
    newService.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="items[${index}][title]" placeholder="Service Title">
                </div>
                <div class="col-md-4">
                    <textarea class="form-control" name="items[${index}][description]" placeholder="Service Description" rows="2"></textarea>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" name="items[${index}][url]" placeholder="Service URL">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeServiceItem(this)">×</button>
                </div>
            </div>
        </div>
    `;
    servicesList.appendChild(newService);
}

function removeServiceItem(button) {
    button.closest('.card').remove();
}

// Initialize section fields on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleSectionFields();

    // Initialize stats container visibility
    const showStats = document.getElementById('show_stats');
    if (showStats && showStats.checked) {
        document.getElementById('stats-container').style.display = 'block';
    }
});
</script>
@endsection
