@extends('admin.layouts.app')

@section('title', 'Home Page SEO Settings')

@section('content')
<div class="admin-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-dark">
                                    <i class="fas fa-search text-primary me-2"></i>
                                    Home Page SEO Settings
                                </h4>
                                <p class="text-muted mb-0 mt-1">Manage SEO meta tags for the entire home page</p>
                            </div>
                            <a href="{{ route('admin.home-content.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Content
                            </a>
                        </div>
                    </div>

                    <div class="card-body p-4">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        <form action="{{ route('admin.home-seo.update') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                <!-- Basic Meta Tags -->
                                <div class="col-lg-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-tags me-2"></i>Basic Meta Tags
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="home_meta_title" class="form-label">Meta Title</label>
                                                <input type="text" class="form-control @error('home_meta_title') is-invalid @enderror" 
                                                       id="home_meta_title" name="home_meta_title" 
                                                       value="{{ old('home_meta_title', $seoSettings['home_meta_title']) }}" 
                                                       maxlength="255" required>
                                                <div class="form-text">Recommended: 50-60 characters</div>
                                                @error('home_meta_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="mb-3">
                                                <label for="home_meta_description" class="form-label">Meta Description</label>
                                                <textarea class="form-control @error('home_meta_description') is-invalid @enderror" 
                                                          id="home_meta_description" name="home_meta_description" 
                                                          rows="4" maxlength="500" required>{{ old('home_meta_description', $seoSettings['home_meta_description']) }}</textarea>
                                                <div class="form-text">Recommended: 150-160 characters</div>
                                                @error('home_meta_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="mb-3">
                                                <label for="home_meta_keywords" class="form-label">Meta Keywords</label>
                                                <input type="text" class="form-control @error('home_meta_keywords') is-invalid @enderror" 
                                                       id="home_meta_keywords" name="home_meta_keywords" 
                                                       value="{{ old('home_meta_keywords', $seoSettings['home_meta_keywords']) }}">
                                                <div class="form-text">Comma-separated keywords (optional)</div>
                                                @error('home_meta_keywords')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Open Graph Tags -->
                                <div class="col-lg-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="fab fa-facebook me-2"></i>Open Graph Tags
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="home_og_title" class="form-label">OG Title</label>
                                                <input type="text" class="form-control @error('home_og_title') is-invalid @enderror" 
                                                       id="home_og_title" name="home_og_title" 
                                                       value="{{ old('home_og_title', $seoSettings['home_og_title']) }}" 
                                                       maxlength="255" required>
                                                <div class="form-text">For social media sharing</div>
                                                @error('home_og_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="mb-3">
                                                <label for="home_og_description" class="form-label">OG Description</label>
                                                <textarea class="form-control @error('home_og_description') is-invalid @enderror" 
                                                          id="home_og_description" name="home_og_description" 
                                                          rows="3" maxlength="500" required>{{ old('home_og_description', $seoSettings['home_og_description']) }}</textarea>
                                                <div class="form-text">For social media sharing</div>
                                                @error('home_og_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="mb-3">
                                                <label for="home_og_image" class="form-label">OG Image</label>
                                                @if($seoSettings['home_og_image'])
                                                    <div class="mb-2">
                                                        <img src="{{ asset('storage/' . $seoSettings['home_og_image']) }}" 
                                                             alt="Current OG image" class="img-thumbnail" 
                                                             style="max-width: 200px; max-height: 150px;">
                                                        <div class="form-text">Current OG image</div>
                                                    </div>
                                                @endif
                                                <input type="file" class="form-control @error('home_og_image') is-invalid @enderror" 
                                                       id="home_og_image" name="home_og_image" accept="image/*">
                                                <div class="form-text">Recommended: 1200x630px. Leave empty to keep current image.</div>
                                                @error('home_og_image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update SEO Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Character count for meta fields
    function updateCharCount(input, maxLength) {
        const current = input.val().length;
        const remaining = maxLength - current;
        const color = remaining < 10 ? 'text-danger' : remaining < 30 ? 'text-warning' : 'text-success';
        
        let helpText = input.siblings('.form-text');
        if (helpText.length) {
            const originalText = helpText.text().split('(')[0].trim();
            helpText.html(`${originalText} <span class="${color}">(${current}/${maxLength})</span>`);
        }
    }

    // Add character counting
    $('#home_meta_title').on('input', function() {
        updateCharCount($(this), 255);
    });

    $('#home_meta_description').on('input', function() {
        updateCharCount($(this), 500);
    });

    $('#home_og_title').on('input', function() {
        updateCharCount($(this), 255);
    });

    $('#home_og_description').on('input', function() {
        updateCharCount($(this), 500);
    });

    // Initialize character counts
    updateCharCount($('#home_meta_title'), 255);
    updateCharCount($('#home_meta_description'), 500);
    updateCharCount($('#home_og_title'), 255);
    updateCharCount($('#home_og_description'), 500);
});
</script>
@endpush
