<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Admin Login - {{ config('app.name', 'Hestia Abodes') }}</title>

    <!-- Preload Critical Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Styles -->
    @vite(['resources/css/admin.css'])

    <!-- Additional Meta Tags -->
    <meta name="description" content="Hestia Abodes Admin Panel - Secure Login">
    <meta name="robots" content="noindex, nofollow">
</head>
<body>
    <div class="login-wrapper">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <!-- You can replace this with your actual logo -->
                    <img src="{{ asset('images/logo-white.png') }}" alt="Hestia Abodes Logo" style="display: none;" onload="this.style.display='block'; this.nextElementSibling.style.display='none';">
                    <i class="fas fa-home"></i>
                </div>
                <h1 class="login-title">Hestia Abodes</h1>
                <p class="login-subtitle">Welcome back to your admin panel</p>
            </div>

            <!-- Flash Messages -->
            @if(session('success'))
                <div class="alert alert-success mb-3">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                </div>
            @endif

            <form method="POST" action="{{ route('admin.login.post') }}" class="login-form">
                @csrf

                <!-- Email Address -->
                <div class="form-floating form-group">
                    <input id="email"
                           type="email"
                           name="email"
                           value="{{ old('email') }}"
                           required
                           autofocus
                           autocomplete="username"
                           class="form-control @error('email') is-invalid @enderror"
                           placeholder="<EMAIL>">
                    <label for="email" class="form-label">Email Address</label>
                    @error('email')
                        <div class="invalid-feedback">
                            <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Password -->
                <div class="form-floating form-group">
                    <input id="password"
                           type="password"
                           name="password"
                           required
                           autocomplete="current-password"
                           class="form-control @error('password') is-invalid @enderror"
                           placeholder="Password">
                    <label for="password" class="form-label">Password</label>
                    @error('password')
                        <div class="invalid-feedback">
                            <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                        </div>
                    @enderror
                </div>

                <!-- Remember Me -->
                <div class="form-group">
                    <div class="modern-checkbox">
                        <input class="checkbox-input"
                               type="checkbox"
                               name="remember"
                               id="remember"
                               {{ old('remember') ? 'checked' : '' }}>
                        <label class="checkbox-label" for="remember">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-text">Keep me signed in</span>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-group mb-0">
                    <button type="submit" class="btn-admin btn-admin-primary btn-admin-xl w-100 login-btn">
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Sign In to Dashboard
                        </span>
                        <div class="btn-loader" style="display: none;">
                            <div class="spinner"></div>
                        </div>
                    </button>
                </div>
            </form>

            <div class="text-center mt-4">
                <p class="text-muted">
                    <small>© {{ date('Y') }} {{ config('app.name', 'Hestia Abodes') }}. All rights reserved.</small>
                </p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    @vite(['resources/js/admin.js'])

    <script>
        // Enhanced login page interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus on first input with error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.focus();
            }

            // Add floating label behavior
            const formInputs = document.querySelectorAll('.form-control');
            formInputs.forEach(input => {
                // Check if input has value on load
                if (input.value) {
                    input.classList.add('has-value');
                }

                input.addEventListener('input', function() {
                    if (this.value) {
                        this.classList.add('has-value');
                    } else {
                        this.classList.remove('has-value');
                    }
                });
            });

            // Enhanced form submission
            const loginForm = document.querySelector('.login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('.login-btn');
                    const btnText = submitBtn.querySelector('.btn-text');
                    const btnLoader = submitBtn.querySelector('.btn-loader');

                    // Show loading state
                    if (btnText && btnLoader) {
                        btnText.style.opacity = '0';
                        btnLoader.style.display = 'block';
                        submitBtn.disabled = true;
                        submitBtn.style.cursor = 'not-allowed';
                    }
                });
            }

            // Add subtle animations
            const loginCard = document.querySelector('.login-card');
            if (loginCard) {
                setTimeout(() => {
                    loginCard.style.transform = 'translateY(0) scale(1)';
                    loginCard.style.opacity = '1';
                }, 100);
            }

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Alt + L to focus email input
                if (e.altKey && e.key === 'l') {
                    e.preventDefault();
                    document.getElementById('email').focus();
                }
            });
        });
    </script>
</body>
</html>
