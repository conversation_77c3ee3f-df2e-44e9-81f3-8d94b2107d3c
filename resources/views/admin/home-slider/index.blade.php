@extends('admin.layouts.app')

@section('title', 'Home Slider')
@section('page-title', 'Home Slider Management')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4>Home Slider</h4>
    <a href="{{ route('admin.home-slider.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Slider
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($sliders->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Title</th>
                            <th>Subtitle</th>
                            <th>Sort Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sliders as $slider)
                        <tr>
                            <td>
                                @if($slider->image)
                                    <img src="{{ asset('storage/' . $slider->image) }}" alt="{{ $slider->title }}" class="img-thumbnail" style="width: 80px; height: 50px; object-fit: cover;">
                                @else
                                    <span class="text-muted">No Image</span>
                                @endif
                            </td>
                            <td>{{ $slider->title }}</td>
                            <td>{{ $slider->subtitle }}</td>
                            <td>{{ $slider->sort_order ?? 0 }}</td>
                            <td>
                                <span class="badge bg-{{ $slider->is_active ? 'success' : 'secondary' }}">
                                    {{ $slider->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.home-slider.edit', $slider) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.home-slider.destroy', $slider) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            @if($sliders->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $sliders->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No sliders found</h5>
                <p class="text-muted">Create your first slider to get started.</p>
                <a href="{{ route('admin.home-slider.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Slider
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
