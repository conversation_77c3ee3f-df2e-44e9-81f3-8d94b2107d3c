@extends('admin.layouts.app')

@section('title', 'Home Page Content Management')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-1 text-gray-900 fw-bold">
                <i class="fas fa-home text-primary me-2"></i>Home Page Content
            </h1>
            <p class="text-muted mb-0">Manage your website's home page content and sections</p>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-primary btn-lg shadow-sm" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                <i class="fas fa-plus me-2"></i>Add New Section
            </button>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Quick Stats Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-home fa-2x opacity-75"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h5 mb-0">{{ $contents->count() }}</div>
                            <div class="small opacity-75">Total Sections</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h5 mb-0">{{ $contents->where('is_active', true)->count() }}</div>
                            <div class="small opacity-75">Active Sections</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-pause-circle fa-2x opacity-75"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h5 mb-0">{{ $contents->where('is_active', false)->count() }}</div>
                            <div class="small opacity-75">Inactive Sections</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Main Content Card -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-white border-bottom-0 py-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-1 fw-bold text-gray-900">
                                <i class="fas fa-layer-group text-primary me-2"></i>Content Sections
                            </h5>
                            <p class="text-muted mb-0 small">Manage and organize your home page content sections</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#seoSettingsModal">
                                <i class="fas fa-search me-2"></i>SEO Settings
                            </button>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                                <i class="fas fa-plus me-2"></i>Add Section
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">

                    <!-- Sections Management -->
                    <div class="row g-4">
                        @foreach($sections as $sectionKey => $sectionName)
                            @php
                                $content = $contents->get($sectionKey);
                                $statusClass = $content && $content->is_active ? 'success' : 'secondary';
                                $statusIcon = $content && $content->is_active ? 'check-circle' : 'pause-circle';
                            @endphp
                            <div class="col-lg-6 col-xl-4">
                                <div class="card border-0 shadow-sm h-100 hover-lift">
                                    <div class="card-header bg-{{ $statusClass }} bg-opacity-10 border-0 py-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-{{ $statusIcon }} text-{{ $statusClass }} me-2"></i>
                                                <h6 class="mb-0 fw-bold text-{{ $statusClass }}">{{ $sectionName }}</h6>
                                            </div>
                                            @if($content)
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle"
                                                           type="checkbox"
                                                           data-id="{{ $content->id }}"
                                                           {{ $content->is_active ? 'checked' : '' }}>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="card-body p-4">
                                        @if($content)
                                            <!-- Content Preview -->
                                            <div class="mb-3">
                                                <div class="d-flex align-items-start mb-2">
                                                    <i class="fas fa-heading text-muted me-2 mt-1"></i>
                                                    <div>
                                                        <small class="text-muted d-block">Title</small>
                                                        <span class="fw-medium">{{ $content->title ?? 'Not set' }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            @if($content->subtitle)
                                                <div class="mb-3">
                                                    <div class="d-flex align-items-start">
                                                        <i class="fas fa-text-height text-muted me-2 mt-1"></i>
                                                        <div>
                                                            <small class="text-muted d-block">Subtitle</small>
                                                            <span class="text-sm">{{ Str::limit($content->subtitle, 50) }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            @if($content->description)
                                                <div class="mb-3">
                                                    <div class="d-flex align-items-start">
                                                        <i class="fas fa-align-left text-muted me-2 mt-1"></i>
                                                        <div>
                                                            <small class="text-muted d-block">Description</small>
                                                            <span class="text-sm text-muted">{{ Str::limit($content->description, 80) }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            @if($content->image)
                                                <div class="mb-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-image text-muted me-2"></i>
                                                        <div>
                                                            <small class="text-muted d-block">Image</small>
                                                            <span class="badge bg-info bg-opacity-10 text-info">
                                                                <i class="fas fa-check me-1"></i>Image Set
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            <!-- Action Buttons -->
                                            <div class="d-flex gap-2 mt-4">
                                                <a href="{{ route('admin.home-content.manage', $sectionKey) }}"
                                                   class="btn btn-primary btn-sm flex-fill">
                                                    <i class="fas fa-cogs me-1"></i>Manage
                                                </a>
                                                <button type="button" class="btn btn-outline-danger btn-sm delete-section"
                                                        data-id="{{ $content->id }}" title="Delete Section">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        @else
                                            <!-- Empty State -->
                                            <div class="text-center py-4">
                                                <div class="mb-3">
                                                    <i class="fas fa-plus-circle fa-3x text-muted opacity-50"></i>
                                                </div>
                                                <p class="text-muted mb-3">No content configured for this section</p>
                                                <a href="{{ route('admin.home-content.create', ['section' => $sectionKey]) }}"
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-plus me-1"></i>Add Content
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Status Toggle
    $(document).on('change', '.status-toggle', function() {
        const $toggle = $(this);
        const id = $toggle.data('id');
        const isActive = $toggle.is(':checked');

        $.post('/admin/home-content/' + id + '/toggle-status', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message || 'Status updated successfully');
                // Don't reload, just update the card border
                $toggle.closest('.card').toggleClass('border-success border-secondary');
            } else {
                toastr.error(response.message || 'Failed to update status');
                $toggle.prop('checked', !isActive);
            }
        })
        .fail(function(xhr) {
            console.error('Status toggle failed:', xhr);
            toastr.error('Failed to update status');
            $toggle.prop('checked', !isActive);
        });
    });

    // Delete Section
    $(document).on('click', '.delete-section', function() {
        const id = $(this).data('id');

        if (confirm('Are you sure you want to delete this section? This action cannot be undone.')) {
            $.ajax({
                url: '/admin/home-content/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                }
            })
            .done(function(response) {
                toastr.success('Section deleted successfully');
                location.reload();
            })
            .fail(function() {
                toastr.error('Failed to delete section');
            });
        }
    });
    // SEO Form Submission
    $('#seoForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '{{ route("admin.home-content.update-seo") }}',
            method: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    $('#seoSettingsModal').modal('hide');
                } else {
                    toastr.error('Failed to update SEO settings');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = 'Validation errors:\n';
                    Object.keys(errors).forEach(key => {
                        errorMessage += `- ${errors[key][0]}\n`;
                    });
                    toastr.error(errorMessage);
                } else {
                    toastr.error('Failed to update SEO settings');
                }
            }
        });
    });
});
</script>
@endpush

<!-- SEO Settings Modal -->
<div class="modal fade" id="seoSettingsModal" tabindex="-1" aria-labelledby="seoSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="seoSettingsModalLabel">
                    <i class="fas fa-search me-2"></i>Home Page SEO Settings
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="seoForm">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row g-4">
                        <!-- Meta Title -->
                        <div class="col-12">
                            <label for="home_meta_title" class="form-label fw-bold">
                                <i class="fas fa-heading text-primary me-2"></i>Meta Title
                            </label>
                            <input type="text" class="form-control" id="home_meta_title" name="home_meta_title"
                                   value="{{ $seoSettings['home_meta_title'] }}" maxlength="255" required>
                            <div class="form-text">Recommended: 50-60 characters</div>
                        </div>

                        <!-- Meta Description -->
                        <div class="col-12">
                            <label for="home_meta_description" class="form-label fw-bold">
                                <i class="fas fa-align-left text-primary me-2"></i>Meta Description
                            </label>
                            <textarea class="form-control" id="home_meta_description" name="home_meta_description"
                                      rows="3" maxlength="500" required>{{ $seoSettings['home_meta_description'] }}</textarea>
                            <div class="form-text">Recommended: 150-160 characters</div>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="col-12">
                            <label for="home_meta_keywords" class="form-label fw-bold">
                                <i class="fas fa-tags text-primary me-2"></i>Meta Keywords
                            </label>
                            <input type="text" class="form-control" id="home_meta_keywords" name="home_meta_keywords"
                                   value="{{ $seoSettings['home_meta_keywords'] }}" maxlength="255" required>
                            <div class="form-text">Separate keywords with commas</div>
                        </div>

                        <!-- Open Graph Title -->
                        <div class="col-md-6">
                            <label for="home_og_title" class="form-label fw-bold">
                                <i class="fab fa-facebook text-primary me-2"></i>OG Title
                            </label>
                            <input type="text" class="form-control" id="home_og_title" name="home_og_title"
                                   value="{{ $seoSettings['home_og_title'] }}" maxlength="255" required>
                        </div>

                        <!-- Open Graph Image -->
                        <div class="col-md-6">
                            <label for="home_og_image" class="form-label fw-bold">
                                <i class="fas fa-image text-primary me-2"></i>OG Image URL
                            </label>
                            <input type="url" class="form-control" id="home_og_image" name="home_og_image"
                                   value="{{ $seoSettings['home_og_image'] }}" maxlength="255">
                        </div>

                        <!-- Open Graph Description -->
                        <div class="col-12">
                            <label for="home_og_description" class="form-label fw-bold">
                                <i class="fab fa-facebook text-primary me-2"></i>OG Description
                            </label>
                            <textarea class="form-control" id="home_og_description" name="home_og_description"
                                      rows="3" maxlength="500" required>{{ $seoSettings['home_og_description'] }}</textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save me-2"></i>Update SEO Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
