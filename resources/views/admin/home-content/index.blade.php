@extends('admin.layouts.app')

@section('title', 'Home Page Content Management')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="h3 mb-0">Home Page Content</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                <i class="fas fa-plus me-2"></i>Add Section
            </button>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-home me-2"></i>Manage Home Page Sections
                    </h5>
                </div>
                <div class="card-body">
                    <!-- SEO Meta Tags Section -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-search me-2"></i>SEO Information</h6>
                        <p class="mb-0">Configure SEO meta tags in the Hero Section for better search engine optimization.</p>
                    </div>

                    <!-- Sections Management -->
                    <div class="row">
                        @foreach($sections as $sectionKey => $sectionName)
                            @php
                                $content = $contents->get($sectionKey);
                            @endphp
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="card border {{ $content && $content->is_active ? 'border-success' : 'border-secondary' }}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ $sectionName }}</h6>
                                        @if($content)
                                            <div class="form-check form-switch">
                                                <input class="form-check-input status-toggle" 
                                                       type="checkbox" 
                                                       data-id="{{ $content->id }}"
                                                       {{ $content->is_active ? 'checked' : '' }}>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="card-body">
                                        @if($content)
                                            <div class="mb-2">
                                                <strong>Title:</strong> {{ $content->title ?? 'Not set' }}
                                            </div>
                                            @if($content->subtitle)
                                                <div class="mb-2">
                                                    <strong>Subtitle:</strong> {{ Str::limit($content->subtitle, 50) }}
                                                </div>
                                            @endif
                                            @if($content->description)
                                                <div class="mb-2">
                                                    <strong>Description:</strong> {{ Str::limit($content->description, 100) }}
                                                </div>
                                            @endif
                                            @if($content->image)
                                                <div class="mb-2">
                                                    <img src="{{ $content->image_url }}" alt="Section Image" class="img-thumbnail" style="max-height: 60px;">
                                                </div>
                                            @endif
                                            <div class="btn-group w-100">
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-section" 
                                                        data-section="{{ $sectionKey }}"
                                                        data-content="{{ json_encode($content) }}">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-section" 
                                                        data-id="{{ $content->id }}">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        @else
                                            <p class="text-muted mb-3">No content configured</p>
                                            <button type="button" class="btn btn-sm btn-primary w-100 add-section" 
                                                    data-section="{{ $sectionKey }}">
                                                <i class="fas fa-plus"></i> Add Content
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Section Modal -->
<div class="modal fade" id="sectionModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sectionModalTitle">Add Section Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sectionForm" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <input type="hidden" id="sectionMethod" name="_method" value="POST">
                    <input type="hidden" id="sectionKey" name="section">
                    
                    <div class="row">
                        <!-- Basic Content -->
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Basic Content</h6>
                            
                            <div class="mb-3">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" class="form-control" id="title" name="title">
                            </div>
                            
                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="subtitle" name="subtitle">
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="button_text" class="form-label">Button Text</label>
                                        <input type="text" class="form-control" id="button_text" name="button_text">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="button_link" class="form-label">Button Link</label>
                                        <input type="text" class="form-control" id="button_link" name="button_link">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Section Image</label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <div id="currentImage" class="mt-2"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sort_order" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- SEO Meta Tags -->
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">SEO Meta Tags</h6>
                            
                            <div class="mb-3">
                                <label for="meta_title" class="form-label">Meta Title</label>
                                <input type="text" class="form-control" id="meta_title" name="meta_title" maxlength="255">
                                <div class="form-text">Recommended: 50-60 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="meta_description" class="form-label">Meta Description</label>
                                <textarea class="form-control" id="meta_description" name="meta_description" rows="3" maxlength="500"></textarea>
                                <div class="form-text">Recommended: 150-160 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                <input type="text" class="form-control" id="meta_keywords" name="meta_keywords">
                                <div class="form-text">Comma-separated keywords</div>
                            </div>
                            
                            <h6 class="border-bottom pb-2 mb-3 mt-4">Open Graph Tags</h6>
                            
                            <div class="mb-3">
                                <label for="og_title" class="form-label">OG Title</label>
                                <input type="text" class="form-control" id="og_title" name="og_title">
                            </div>
                            
                            <div class="mb-3">
                                <label for="og_description" class="form-label">OG Description</label>
                                <textarea class="form-control" id="og_description" name="og_description" rows="2"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="og_image" class="form-label">OG Image</label>
                                <input type="file" class="form-control" id="og_image" name="og_image" accept="image/*">
                                <div class="form-text">Recommended: 1200x630px</div>
                                <div id="currentOgImage" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Section</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Section Modal (for selecting section type) -->
<div class="modal fade" id="addSectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Section</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    @foreach($sections as $sectionKey => $sectionName)
                        @if(!$contents->has($sectionKey))
                            <button type="button" class="list-group-item list-group-item-action add-section" 
                                    data-section="{{ $sectionKey }}" data-bs-dismiss="modal">
                                <i class="fas fa-plus me-2"></i>{{ $sectionName }}
                            </button>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Add/Edit Section
    $(document).on('click', '.add-section, .edit-section', function() {
        const section = $(this).data('section');
        const content = $(this).data('content');
        const isEdit = $(this).hasClass('edit-section');
        
        $('#sectionModalTitle').text(isEdit ? 'Edit Section Content' : 'Add Section Content');
        $('#sectionKey').val(section);
        $('#sectionMethod').val(isEdit ? 'PUT' : 'POST');
        
        if (isEdit && content) {
            // Populate form with existing data
            $('#title').val(content.title || '');
            $('#subtitle').val(content.subtitle || '');
            $('#description').val(content.description || '');
            $('#button_text').val(content.button_text || '');
            $('#button_link').val(content.button_link || '');
            $('#sort_order').val(content.sort_order || 0);
            $('#is_active').prop('checked', content.is_active);
            $('#meta_title').val(content.meta_title || '');
            $('#meta_description').val(content.meta_description || '');
            $('#meta_keywords').val(content.meta_keywords || '');
            $('#og_title').val(content.og_title || '');
            $('#og_description').val(content.og_description || '');
            
            // Show current images
            if (content.image) {
                $('#currentImage').html('<img src="' + content.image_url + '" class="img-thumbnail mt-2" style="max-height: 100px;">');
            }
            if (content.og_image) {
                $('#currentOgImage').html('<img src="' + content.og_image_url + '" class="img-thumbnail mt-2" style="max-height: 100px;">');
            }
            
            $('#sectionForm').attr('action', '{{ route("admin.home-content.index") }}/' + content.id);
        } else {
            // Reset form for new section
            $('#sectionForm')[0].reset();
            $('#is_active').prop('checked', true);
            $('#currentImage, #currentOgImage').empty();
            $('#sectionForm').attr('action', '{{ route("admin.home-content.store") }}');
        }
        
        $('#sectionModal').modal('show');
    });
    
    // Status Toggle
    $(document).on('change', '.status-toggle', function() {
        const id = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.post('{{ route("admin.home-content.index") }}/' + id + '/toggle-status', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload();
            }
        })
        .fail(function() {
            toastr.error('Failed to update status');
            $(this).prop('checked', !isActive);
        });
    });
    
    // Delete Section
    $(document).on('click', '.delete-section', function() {
        const id = $(this).data('id');
        
        if (confirm('Are you sure you want to delete this section?')) {
            $.ajax({
                url: '{{ route("admin.home-content.index") }}/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                }
            })
            .done(function(response) {
                toastr.success('Section deleted successfully');
                location.reload();
            })
            .fail(function() {
                toastr.error('Failed to delete section');
            });
        }
    });
});
</script>
@endpush
