@extends('admin.layouts.app')

@section('title', 'Home Page Content Management')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="h3 mb-0">Home Page Content</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                <i class="fas fa-plus me-2"></i>Add Section
            </button>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-home me-2"></i>Manage Home Page Sections
                        </h5>
                        <a href="{{ route('admin.home-seo.index') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-search me-2"></i>SEO Settings
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- SEO Meta Tags Section -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-search me-2"></i>SEO Information</h6>
                        <p class="mb-2">SEO meta tags are now managed separately for the entire home page.</p>
                        <a href="{{ route('admin.home-seo.index') }}" class="btn btn-sm btn-info">
                            <i class="fas fa-cog me-1"></i>Manage SEO Settings
                        </a>
                    </div>

                    <!-- Sections Management -->
                    <div class="row">
                        @foreach($sections as $sectionKey => $sectionName)
                            @php
                                $content = $contents->get($sectionKey);
                            @endphp
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="card border {{ $content && $content->is_active ? 'border-success' : 'border-secondary' }}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ $sectionName }}</h6>
                                        @if($content)
                                            <div class="form-check form-switch">
                                                <input class="form-check-input status-toggle" 
                                                       type="checkbox" 
                                                       data-id="{{ $content->id }}"
                                                       {{ $content->is_active ? 'checked' : '' }}>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="card-body">
                                        @if($content)
                                            <div class="mb-2">
                                                <strong>Title:</strong> {{ $content->title ?? 'Not set' }}
                                            </div>
                                            @if($content->subtitle)
                                                <div class="mb-2">
                                                    <strong>Subtitle:</strong> {{ Str::limit($content->subtitle, 50) }}
                                                </div>
                                            @endif
                                            @if($content->description)
                                                <div class="mb-2">
                                                    <strong>Description:</strong> {{ Str::limit($content->description, 100) }}
                                                </div>
                                            @endif
                                            @if($content->image)
                                                <div class="mb-2">
                                                    <img src="{{ $content->image_url }}" alt="Section Image" class="img-thumbnail" style="max-height: 60px;">
                                                </div>
                                            @endif
                                            <div class="btn-group w-100">
                                                <a href="{{ route('admin.home-content.manage', $sectionKey) }}"
                                                   class="btn btn-sm btn-primary">
                                                    <i class="fas fa-cogs"></i> Manage
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-section"
                                                        data-id="{{ $content->id }}">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>


                                        @else
                                            <p class="text-muted mb-3">No content configured</p>
                                            <a href="{{ route('admin.home-content.create', ['section' => $sectionKey]) }}"
                                               class="btn btn-sm btn-primary w-100">
                                                <i class="fas fa-plus"></i> Add Content
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Status Toggle
    $(document).on('change', '.status-toggle', function() {
        const $toggle = $(this);
        const id = $toggle.data('id');
        const isActive = $toggle.is(':checked');

        $.post('/admin/home-content/' + id + '/toggle-status', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message || 'Status updated successfully');
                // Don't reload, just update the card border
                $toggle.closest('.card').toggleClass('border-success border-secondary');
            } else {
                toastr.error(response.message || 'Failed to update status');
                $toggle.prop('checked', !isActive);
            }
        })
        .fail(function(xhr) {
            console.error('Status toggle failed:', xhr);
            toastr.error('Failed to update status');
            $toggle.prop('checked', !isActive);
        });
    });

    // Delete Section
    $(document).on('click', '.delete-section', function() {
        const id = $(this).data('id');

        if (confirm('Are you sure you want to delete this section? This action cannot be undone.')) {
            $.ajax({
                url: '/admin/home-content/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                }
            })
            .done(function(response) {
                toastr.success('Section deleted successfully');
                location.reload();
            })
            .fail(function() {
                toastr.error('Failed to delete section');
            });
        }
    });
});
</script>
@endpush
