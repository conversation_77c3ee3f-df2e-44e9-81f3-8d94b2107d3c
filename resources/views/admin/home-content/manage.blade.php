@extends('admin.layouts.app')

@section('title', 'Manage ' . $sections[$section])

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Manage {{ $sections[$section] }}
                    </h4>
                    <div>
                        <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Overview
                        </a>
                        @if($mainContent)
                            <a href="{{ route('admin.home-content.create', ['section' => $section, 'parent_id' => $mainContent->id]) }}" 
                               class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Sub-Content
                            </a>
                        @else
                            <a href="{{ route('admin.home-content.create', ['section' => $section]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Main Content
                            </a>
                        @endif
                    </div>
                </div>

                <div class="card-body">
                    @if($mainContent)
                        <!-- Main Content Section -->
                        <div class="main-content-section mb-5">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="text-primary">
                                    <i class="fas fa-star"></i> Main Content
                                </h5>
                                <div class="btn-group">
                                    <a href="{{ route('admin.home-content.edit', $mainContent->id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-content" 
                                            data-id="{{ $mainContent->id }}">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>

                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="fw-bold">{{ $mainContent->title }}</h6>
                                            @if($mainContent->subtitle)
                                                <p class="text-muted mb-2">{{ $mainContent->subtitle }}</p>
                                            @endif
                                            @if($mainContent->description)
                                                <p class="mb-2">{{ Str::limit($mainContent->description, 200) }}</p>
                                            @endif
                                            @if($mainContent->button_text && $mainContent->button_link)
                                                <span class="badge bg-info">
                                                    Button: {{ $mainContent->button_text }}
                                                </span>
                                            @endif
                                        </div>
                                        <div class="col-md-4 text-end">
                                            @if($mainContent->image)
                                                <img src="{{ $mainContent->image_url }}" 
                                                     alt="{{ $mainContent->title }}" 
                                                     class="img-thumbnail" 
                                                     style="max-width: 150px; max-height: 100px;">
                                            @endif
                                            <div class="mt-2">
                                                <span class="badge {{ $mainContent->is_active ? 'bg-success' : 'bg-secondary' }}">
                                                    {{ $mainContent->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sub-Content Section -->
                        @if($mainContent->children && $mainContent->children->count() > 0)
                            <div class="sub-content-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="text-success">
                                        <i class="fas fa-list"></i> Sub-Content Items ({{ $mainContent->children->count() }})
                                    </h5>
                                    <a href="{{ route('admin.home-content.create', ['section' => $section, 'parent_id' => $mainContent->id]) }}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-plus"></i> Add More
                                    </a>
                                </div>

                                <div class="row">
                                    @foreach($mainContent->children as $index => $child)
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card border-success h-100">
                                                <div class="card-header bg-light d-flex justify-content-between align-items-center py-2">
                                                    <small class="text-muted">Sub-Content #{{ $index + 1 }}</small>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ route('admin.home-content.edit', $child->id) }}" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger btn-sm delete-content" 
                                                                data-id="{{ $child->id }}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <h6 class="card-title">{{ $child->title ?: 'No Title' }}</h6>
                                                    @if($child->subtitle)
                                                        <p class="card-subtitle text-muted small mb-2">{{ $child->subtitle }}</p>
                                                    @endif
                                                    @if($child->description)
                                                        <p class="card-text small">{{ Str::limit($child->description, 100) }}</p>
                                                    @endif
                                                    @if($child->image)
                                                        <img src="{{ $child->image_url }}" 
                                                             alt="{{ $child->title }}" 
                                                             class="img-fluid rounded mb-2" 
                                                             style="max-height: 80px; width: 100%; object-fit: cover;">
                                                    @endif
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span class="badge {{ $child->is_active ? 'bg-success' : 'bg-secondary' }} small">
                                                            {{ $child->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                        @if($child->button_text)
                                                            <small class="text-info">
                                                                <i class="fas fa-link"></i> {{ $child->button_text }}
                                                            </small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <div class="sub-content-section">
                                <div class="text-center py-5">
                                    <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Sub-Content Items</h5>
                                    <p class="text-muted">Add sub-content items to enhance this section</p>
                                    <a href="{{ route('admin.home-content.create', ['section' => $section, 'parent_id' => $mainContent->id]) }}" 
                                       class="btn btn-success">
                                        <i class="fas fa-plus"></i> Add First Sub-Content
                                    </a>
                                </div>
                            </div>
                        @endif
                    @else
                        <!-- No Main Content -->
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
                            <h5 class="text-muted">No Main Content Found</h5>
                            <p class="text-muted">Create the main content for this section first</p>
                            <a href="{{ route('admin.home-content.create', ['section' => $section]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Main Content
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Delete content
    $(document).on('click', '.delete-content', function() {
        const id = $(this).data('id');
        
        if (confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
            $.ajax({
                url: '/admin/home-content/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    toastr.success('Content deleted successfully');
                    location.reload();
                },
                error: function(xhr) {
                    toastr.error('Error deleting content');
                }
            });
        }
    });
});
</script>
@endpush
@endsection
