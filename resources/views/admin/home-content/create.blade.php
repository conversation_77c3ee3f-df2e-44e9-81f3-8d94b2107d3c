@extends('admin.layouts.app')

@section('title', 'Create Content - ' . ($sections[$section] ?? 'Unknown Section'))

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        Create {{ $parentId ? 'Sub-Content' : 'Main Content' }} - {{ $sections[$section] ?? 'Unknown Section' }}
                    </h4>
                    <div>
                        @if($parentId)
                            <a href="{{ route('admin.home-content.manage', $section) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Section
                            </a>
                        @else
                            <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Overview
                            </a>
                        @endif
                    </div>
                </div>

                <form action="{{ route('admin.home-content.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="section" value="{{ $section }}">
                    <input type="hidden" name="parent_id" value="{{ $parentId }}">
                    <input type="hidden" name="item_type" value="{{ $parentId ? 'sub_item' : 'main' }}">

                    <div class="card-body">
                        <div class="row">
                            <!-- Basic Content -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-edit"></i> Basic Content
                                </h5>

                                <div class="mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title') }}" 
                                           placeholder="Enter content title">
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="subtitle" class="form-label">Subtitle</label>
                                    <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                           id="subtitle" name="subtitle" value="{{ old('subtitle') }}" 
                                           placeholder="Enter content subtitle">
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="4" 
                                              placeholder="Enter content description">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="button_text" class="form-label">Button Text</label>
                                            <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                                   id="button_text" name="button_text" value="{{ old('button_text') }}" 
                                                   placeholder="e.g., Learn More">
                                            @error('button_text')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="button_link" class="form-label">Button Link</label>
                                            <input type="text" class="form-control @error('button_link') is-invalid @enderror" 
                                                   id="button_link" name="button_link" value="{{ old('button_link') }}" 
                                                   placeholder="e.g., /about-us">
                                            @error('button_link')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                                                   min="0">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mt-4">
                                                <input class="form-check-input" type="checkbox" id="is_active" 
                                                       name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Active
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Media & SEO -->
                            <div class="col-md-6">
                                <h5 class="text-success mb-3">
                                    <i class="fas fa-image"></i> Media & SEO
                                </h5>

                                <div class="mb-3">
                                    <label for="image" class="form-label">Section Image</label>
                                    <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                           id="image" name="image" accept="image/*">
                                    <div class="form-text">Recommended: 1200x630px</div>
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> SEO meta tags are managed separately for the entire home page.
                                    Individual content sections focus on content only.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            @if($parentId)
                                <a href="{{ route('admin.home-content.manage', $section) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            @else
                                <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            @endif
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Content
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
