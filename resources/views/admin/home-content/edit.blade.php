@extends('admin.layouts.app')

@section('title', 'Edit Content - ' . $homeContent->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Edit {{ $homeContent->parent_id ? 'Sub-Content' : 'Main Content' }} - {{ $sections[$homeContent->section] ?? 'Unknown Section' }}
                    </h4>
                    <div>
                        @if($homeContent->parent_id)
                            <a href="{{ route('admin.home-content.manage', $homeContent->section) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Section
                            </a>
                        @else
                            <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Overview
                            </a>
                        @endif
                    </div>
                </div>

                <form action="{{ route('admin.home-content.update', $homeContent) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="card-body">
                        <div class="row">
                            <!-- Basic Content -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-edit"></i> Basic Content
                                </h5>

                                <div class="mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title', $homeContent->title) }}" 
                                           placeholder="Enter content title">
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="subtitle" class="form-label">Subtitle</label>
                                    <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                           id="subtitle" name="subtitle" value="{{ old('subtitle', $homeContent->subtitle) }}" 
                                           placeholder="Enter content subtitle">
                                    @error('subtitle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="4" 
                                              placeholder="Enter content description">{{ old('description', $homeContent->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="button_text" class="form-label">Button Text</label>
                                            <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                                   id="button_text" name="button_text" value="{{ old('button_text', $homeContent->button_text) }}" 
                                                   placeholder="e.g., Learn More">
                                            @error('button_text')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="button_link" class="form-label">Button Link</label>
                                            <input type="text" class="form-control @error('button_link') is-invalid @enderror" 
                                                   id="button_link" name="button_link" value="{{ old('button_link', $homeContent->button_link) }}" 
                                                   placeholder="e.g., /about-us">
                                            @error('button_link')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $homeContent->sort_order) }}" 
                                                   min="0">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch mt-4">
                                                <input class="form-check-input" type="checkbox" id="is_active" 
                                                       name="is_active" value="1" {{ old('is_active', $homeContent->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Active
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Media & SEO -->
                            <div class="col-md-6">
                                <h5 class="text-success mb-3">
                                    <i class="fas fa-image"></i> Media & SEO
                                </h5>

                                @php
                                    $showIcon = false;
                                    $showImage = false;

                                    // Icon is used for sub-items in services and why_choose_us sections
                                    if ($homeContent->parent_id && in_array($homeContent->section, ['services', 'why_choose_us'])) {
                                        $showIcon = true;
                                    }

                                    // Image is used for:
                                    // - Main content in: hero, about, services, why_choose_us, testimonials
                                    // - Sub-items in: services, why_choose_us, testimonials
                                    if (!$homeContent->parent_id && in_array($homeContent->section, ['hero', 'about', 'services', 'why_choose_us', 'testimonials'])) {
                                        $showImage = true;
                                    } elseif ($homeContent->parent_id && in_array($homeContent->section, ['services', 'why_choose_us', 'testimonials'])) {
                                        $showImage = true;
                                    }
                                @endphp

                                @if($showIcon)
                                    <div class="mb-3">
                                        <label for="icon" class="form-label">Section Icon</label>
                                        <input type="text" class="form-control icon-picker @error('icon') is-invalid @enderror"
                                               id="icon" name="icon" value="{{ old('icon', $homeContent->icon) }}" placeholder="fas fa-home">
                                        <div class="form-text">Choose an icon to represent this content</div>
                                        @error('icon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                @endif

                                @if($showImage)
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Section Image</label>
                                        @if($homeContent->image)
                                            <div class="mb-2">
                                                <img src="{{ $homeContent->image_url }}" alt="Current image"
                                                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                                <div class="form-text">Current image</div>
                                            </div>
                                        @endif
                                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                                               id="image" name="image" accept="image/*">
                                        <div class="form-text">Recommended: 1200x630px. Leave empty to keep current image.</div>
                                        @error('image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                @endif

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> SEO meta tags are managed separately for the entire home page.
                                    Individual content sections focus on content only.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            @if($homeContent->parent_id)
                                <a href="{{ route('admin.home-content.manage', $homeContent->section) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            @else
                                <a href="{{ route('admin.home-content.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            @endif
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Content
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{ asset('css/icon-picker.css') }}" rel="stylesheet">
@endpush

@push('scripts')
<script src="{{ asset('js/icon-picker.js') }}"></script>
@endpush
