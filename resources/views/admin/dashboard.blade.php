@extends('admin.layouts.app')

@section('title', 'Dashboard')

@section('header')
    <div class="breadcrumb-nav">
        <a href="{{ route('admin.dashboard') }}" class="breadcrumb-item active">
            <i class="fas fa-home me-1"></i>Dashboard
        </a>
    </div>
    <h1 class="page-title">Dashboard Overview</h1>
    <p class="page-subtitle">Welcome back, {{ Auth::guard('admin')->user()->name }}! Here's what's happening with your properties today.</p>
    <div class="page-actions">
        <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Project
        </a>
        <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline">
            <i class="fas fa-list me-2"></i>View All Projects
        </a>
    </div>
@endsection

@section('content')
    <!-- Enhanced Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-content">
                <h3>{{ $stats['total_projects'] }}</h3>
                <p>Total Projects</p>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% from last month</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3>{{ $stats['active_projects'] }}</h3>
                <p>Active Projects</p>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% this week</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-content">
                <h3>{{ $stats['featured_projects'] }}</h3>
                <p>Featured Projects</p>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5% this month</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon info">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3>{{ $stats['upcoming_projects'] }}</h3>
                <p>Upcoming Projects</p>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span>+2 new launches</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add Project
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-list me-2"></i>Manage Projects
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="#" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-chart-bar me-2"></i>View Reports
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="#" class="btn-admin btn-admin-outline w-100">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Projects -->
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        Recent Projects
                    </h5>
                    <a href="{{ route('admin.projects.index') }}" class="btn-admin btn-admin-outline btn-admin-sm">
                        <i class="fas fa-external-link-alt me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    @if($stats['recent_projects']->count() > 0)
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Project Name</th>
                                        <th>Location</th>
                                        <th>Starting Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($stats['recent_projects'] as $project)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <strong>{{ $project->name }}</strong>
                                                        @if($project->featured)
                                                            <span class="badge badge-warning ms-1">Featured</span>
                                                        @endif
                                                        <br>
                                                        <small class="text-muted">{{ $project->developer }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>{{ $project->location }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $project->city }}</small>
                                            </td>
                                            <td>
                                                <strong class="text-primary">{{ $project->formatted_starting_price }}</strong>
                                            </td>
                                            <td>
                                                @if($project->status === 'upcoming')
                                                    <span class="badge badge-info">Upcoming</span>
                                                @elseif($project->status === 'ongoing')
                                                    <span class="badge badge-warning">Ongoing</span>
                                                @elseif($project->status === 'ready_to_move')
                                                    <span class="badge badge-success">Ready to Move</span>
                                                @else
                                                    <span class="badge badge-secondary">Completed</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a href="{{ $project->project_url }}"
                                                       class="btn-admin btn-admin-success btn-sm"
                                                       target="_blank"
                                                       title="View on Website">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <a href="{{ route('admin.projects.show', $project) }}"
                                                       class="btn-admin btn-admin-outline btn-sm"
                                                       title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.projects.edit', $project) }}"
                                                       class="btn-admin btn-admin-primary btn-sm"
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <h5 class="empty-title">No Projects Yet</h5>
                                <p class="empty-subtitle">Start building your project portfolio by adding your first project.</p>
                                <a href="{{ route('admin.projects.create') }}" class="btn-admin btn-admin-primary btn-admin-lg">
                                    <i class="fas fa-plus me-2"></i>Add Your First Project
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Analytics & Insights -->
        <div class="col-lg-4">
            <div class="admin-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2 text-info"></i>
                        Property Analytics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="analytics-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Avg. Property Value</span>
                            <span class="analytics-value text-primary">₹1.2Cr</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="analytics-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Inquiry Rate</span>
                            <span class="analytics-value text-success">68%</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress success" style="width: 68%"></div>
                        </div>
                    </div>

                    <div class="analytics-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="analytics-label">Conversion Rate</span>
                            <span class="analytics-value text-warning">24%</span>
                        </div>
                        <div class="analytics-bar">
                            <div class="analytics-progress warning" style="width: 24%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Info -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Laravel Version</small>
                        <div><strong>{{ app()->version() }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">PHP Version</small>
                        <div><strong>{{ PHP_VERSION }}</strong></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Environment</small>
                        <div>
                            <span class="badge {{ app()->environment('production') ? 'badge-danger' : 'badge-warning' }}">
                                {{ strtoupper(app()->environment()) }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <small class="text-muted">Last Login</small>
                        <div><strong>{{ now()->format('M d, Y H:i') }}</strong></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .d-grid {
        display: grid;
    }
    
    .gap-2 {
        gap: 0.5rem;
    }
</style>
@endpush
