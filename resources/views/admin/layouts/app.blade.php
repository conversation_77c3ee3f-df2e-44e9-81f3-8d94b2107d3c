<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel') - {{ config('app.name', 'Hest<PERSON> Abo<PERSON>') }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #3498db;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-danger: #e74c3c;
            --admin-light: #ecf0f1;
            --admin-dark: #2c3e50;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-sidebar {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .admin-sidebar .sidebar-brand {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .admin-sidebar .sidebar-brand h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }

        .admin-sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .admin-sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }

        .admin-main {
            margin-left: 250px;
            min-height: 100vh;
        }

        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .admin-content {
            padding: 0 2rem 2rem;
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .admin-card .card-header {
            background: linear-gradient(135deg, var(--admin-light), #ffffff);
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0 !important;
            padding: 1rem 1.5rem;
        }

        .btn-admin {
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-admin-primary {
            background: linear-gradient(135deg, var(--admin-accent), #2980b9);
            color: white;
        }

        .btn-admin-primary:hover {
            background: linear-gradient(135deg, #2980b9, var(--admin-accent));
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .btn-admin-outline {
            border: 2px solid var(--admin-accent);
            color: var(--admin-accent);
            background: transparent;
        }

        .btn-admin-outline:hover {
            background: var(--admin-accent);
            color: white;
        }

        .btn-admin-success {
            background: linear-gradient(135deg, var(--admin-success), #229954);
            color: white;
        }

        .btn-admin-danger {
            background: linear-gradient(135deg, var(--admin-danger), #c0392b);
            color: white;
        }

        .btn-admin-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .btn-admin-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .breadcrumb-item {
            color: #6c757d;
            text-decoration: none;
        }

        .breadcrumb-item:hover {
            color: var(--admin-accent);
        }

        .breadcrumb-item.active {
            color: var(--admin-primary);
            font-weight: 500;
        }

        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #adb5bd;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--admin-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }

        .page-actions {
            margin-bottom: 1.5rem;
        }
    </style>

        .admin-table {
            width: 100%;
            margin-bottom: 0;
        }

        .admin-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: var(--admin-primary);
            padding: 1rem 0.75rem;
        }

        .admin-table td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .badge {
            padding: 0.5em 0.75em;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-info { background-color: var(--admin-accent); color: white; }
        .badge-success { background-color: var(--admin-success); color: white; }
        .badge-warning { background-color: var(--admin-warning); color: white; }
        .badge-danger { background-color: var(--admin-danger); color: white; }
        .badge-secondary { background-color: #6c757d; color: white; }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-icon {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        .empty-title {
            color: var(--admin-primary);
            margin-bottom: 0.5rem;
        }

        .empty-subtitle {
            color: #6c757d;
            margin-bottom: 2rem;
        }

        .color-preview {
            display: inline-block;
            border-radius: 3px;
        }

        .form-control:focus {
            border-color: var(--admin-accent);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background-color: rgba(39, 174, 96, 0.1);
            color: var(--admin-success);
            border-left: 4px solid var(--admin-success);
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--admin-danger);
            border-left: 4px solid var(--admin-danger);
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-main {
                margin-left: 0;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Sidebar -->
    <nav class="admin-sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-home me-2"></i>Hestia Abodes</h4>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.sliders.*') ? 'active' : '' }}" href="{{ route('admin.sliders.index') }}">
                    <i class="fas fa-images"></i> Home Slider
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.home-content.*') ? 'active' : '' }}" href="#">
                    <i class="fas fa-home"></i> Home Content
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.about-us.*') ? 'active' : '' }}" href="{{ route('admin.about-us.index') }}">
                    <i class="fas fa-info-circle"></i> About Us
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.projects.*') ? 'active' : '' }}" href="{{ route('admin.projects.index') }}">
                    <i class="fas fa-building"></i> Projects
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.services.*') ? 'active' : '' }}" href="{{ route('admin.services.index') }}">
                    <i class="fas fa-cogs"></i> Our Services
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.contact.*') ? 'active' : '' }}" href="{{ route('admin.contact.index') }}">
                    <i class="fas fa-envelope"></i> Contact
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.blogs.*') ? 'active' : '' }}" href="{{ route('admin.blogs.index') }}">
                    <i class="fas fa-blog"></i> Blogs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}" href="#">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Header -->
        <div class="admin-header">
            @yield('header')
        </div>

        <!-- Content -->
        <div class="admin-content">
            <!-- Flash Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @yield('content')
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @stack('scripts')
</body>
</html>