<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Dashboard') - {{ config('app.name', 'Hestia Abodes') }}</title>

    <!-- Favicon -->
    @php
        $settings = \App\Models\Setting::getAllSettings();
    @endphp
    <link rel="icon" type="image/x-icon" href="{{ logo_url($settings['favicon'] ?? null) }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/admin.css', 'resources/js/admin.js'])
    
    @stack('styles')
</head>
<body>
    <div class="admin-wrapper">
        <!-- Ultra Modern Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="{{ route('admin.dashboard') }}" class="sidebar-logo">
                    @php
                        $settings = \App\Models\Setting::getAllSettings();
                    @endphp
                    @if(!empty($settings['logo']))
                        <img src="{{ logo_url($settings['logo']) }}" alt="{{ $settings['company_name'] ?? 'Hestia Abodes' }}" style="height: 40px; width: auto;">
                    @else
                        <i class="fas fa-home"></i>
                        <span class="logo-text">{{ $settings['company_name'] ?? 'Hestia Abodes' }}</span>
                    @endif
                </a>
            </div>

            <nav class="sidebar-nav">
                <!-- Main Navigation -->
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                               href="{{ route('admin.dashboard') }}"
                               data-tooltip="Dashboard">
                                <div class="nav-link-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span class="nav-text">Dashboard</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.home-content.*') ? 'active' : '' }}"
                               href="{{ route('admin.home-content.index') }}"
                               data-tooltip="Home Page Content">
                                <div class="nav-link-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <span class="nav-text">Home Content</span>
                            </a>
                        </li>



                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.projects.*') ? 'active' : '' }}"
                               href="{{ route('admin.projects.index') }}"
                               data-tooltip="Projects">
                                <div class="nav-link-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <span class="nav-text">Projects</span>
                                <span class="nav-badge">{{ \App\Models\Project::count() }}</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.sliders.*') ? 'active' : '' }}"
                               href="{{ route('admin.sliders.index') }}"
                               data-tooltip="Sliders">
                                <div class="nav-link-icon">
                                    <i class="fas fa-images"></i>
                                </div>
                                <span class="nav-text">Sliders</span>
                                <span class="nav-badge">{{ \App\Models\Slider::count() }}</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.testimonials.*') ? 'active' : '' }}"
                               href="{{ route('admin.testimonials.index') }}"
                               data-tooltip="Testimonials">
                                <div class="nav-link-icon">
                                    <i class="fas fa-quote-left"></i>
                                </div>
                                <span class="nav-text">Testimonials</span>
                                <span class="nav-badge">{{ \App\Models\Testimonial::active()->count() }}</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <ul class="nav flex-column">
                        
                        
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}"
                               href="{{ route('admin.settings.index') }}" data-tooltip="Settings">
                                <div class="nav-link-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span class="nav-text">Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Account</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.profile.*') ? 'active' : '' }}"
                               href="{{ route('admin.profile.show') }}" data-tooltip="Profile">
                                <div class="nav-link-icon">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <span class="nav-text">Profile</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                               data-tooltip="Logout">
                                <div class="nav-link-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <span class="nav-text">Logout</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Top Navigation -->
            <header class="admin-topbar">
                <div class="topbar-left">
                    <button class="sidebar-toggle d-none d-lg-block" id="sidebarToggle" type="button">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle" type="button">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                
                <div class="topbar-right">
                    <span class="text-muted me-3" id="headerClock"></span>
                    <div class="dropdown">
                        <div class="topbar-user" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">
                                {{ strtoupper(substr(Auth::guard('admin')->user()->name, 0, 1)) }}
                            </div>
                            <div class="user-info">
                                <div class="user-name">{{ Auth::guard('admin')->user()->name }}</div>
                                <div class="user-role">{{ ucfirst(Auth::guard('admin')->user()->role) }}</div>
                            </div>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.profile.show') }}">
                                    <i class="fas fa-user-circle me-2"></i>My Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="admin-content">
                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm rounded-3 mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <strong>Success!</strong> {{ session('success') }}
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show border-0 shadow-sm rounded-3 mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <strong>Error!</strong> {{ session('error') }}
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                @endif

                @if(session('warning'))
                    <div class="alert alert-warning alert-dismissible fade show border-0 shadow-sm rounded-3 mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <strong>Warning!</strong> {{ session('warning') }}
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                @endif

                <!-- Breadcrumb -->
                @if(!request()->routeIs('admin.dashboard'))
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb bg-transparent p-0 mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Dashboard
                                </a>
                            </li>
                            @if(request()->routeIs('admin.home-content.*'))
                                <li class="breadcrumb-item">
                                    <a href="{{ route('admin.home-content.index') }}" class="text-decoration-none">Home Content</a>
                                </li>
                                @if(!request()->routeIs('admin.home-content.index'))
                                    <li class="breadcrumb-item active" aria-current="page">
                                        @if(request()->routeIs('admin.home-content.create'))
                                            Create Content
                                        @elseif(request()->routeIs('admin.home-content.edit'))
                                            Edit Content
                                        @elseif(request()->routeIs('admin.home-content.manage'))
                                            Manage Section
                                        @endif
                                    </li>
                                @endif

                            @elseif(request()->routeIs('admin.settings.*'))
                                <li class="breadcrumb-item active" aria-current="page">Settings</li>
                            @endif
                        </ol>
                    </nav>
                @endif

                <!-- Page Header -->
                @hasSection('header')
                    <div class="page-header bg-white rounded-3 shadow-sm p-4 mb-4">
                        @yield('header')
                    </div>
                @endif

                <!-- Main Content -->
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Logout Form -->
    <form id="logout-form" action="{{ route('admin.logout') }}" method="POST" style="display: none;">
        @csrf
    </form>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Toastr for notifications -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <script>
        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
    </script>

    @stack('scripts')
</body>
</html>
