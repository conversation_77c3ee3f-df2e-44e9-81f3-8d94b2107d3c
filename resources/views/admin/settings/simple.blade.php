@extends('admin.layouts.app')

@section('title', 'Website Settings')

@section('content')
<div class="admin-header">
    <div class="admin-header-content">
        <h1 class="admin-title">
            <i class="fas fa-cog me-2"></i>
            Website Settings
        </h1>
    </div>
</div>

<div class="admin-content">
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Simple Settings Form -->
    <form action="{{ route('admin.settings.simple.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Website Basic Info -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Website Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="site_name" class="form-label">Website Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="{{ old('site_name', $settings['site_name'] ?? 'Hestia Abodes') }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="site_tagline" class="form-label">Website Tagline</label>
                        <input type="text" class="form-control" id="site_tagline" name="site_tagline" 
                               value="{{ old('site_tagline', $settings['site_tagline'] ?? 'Premium Real Estate Solutions') }}">
                    </div>
                    <div class="col-12 mb-3">
                        <label for="site_description" class="form-label">Website Description</label>
                        <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ old('site_description', $settings['site_description'] ?? 'Your trusted partner in real estate solutions') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Company Information -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Company Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="company_name" class="form-label">Company Name</label>
                        <input type="text" class="form-control" id="company_name" name="company_name" 
                               value="{{ old('company_name', $settings['company_name'] ?? 'Hestia Abodes') }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="company_tagline" class="form-label">Company Tagline</label>
                        <input type="text" class="form-control" id="company_tagline" name="company_tagline" 
                               value="{{ old('company_tagline', $settings['company_tagline'] ?? 'Building Dreams, Creating Homes') }}">
                    </div>
                    <div class="col-12 mb-3">
                        <label for="company_description" class="form-label">Company Description</label>
                        <textarea class="form-control" id="company_description" name="company_description" rows="3">{{ old('company_description', $settings['company_description'] ?? 'We are a leading real estate company dedicated to helping you find your perfect home.') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-phone me-2"></i>Contact Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                               value="{{ old('contact_phone', $settings['contact_phone'] ?? '+****************') }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                               value="{{ old('contact_email', $settings['contact_email'] ?? '<EMAIL>') }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_whatsapp" class="form-label">WhatsApp Number</label>
                        <input type="tel" class="form-control" id="contact_whatsapp" name="contact_whatsapp" 
                               value="{{ old('contact_whatsapp', $settings['contact_whatsapp'] ?? '+****************') }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_address" class="form-label">Address</label>
                        <input type="text" class="form-control" id="contact_address" name="contact_address" 
                               value="{{ old('contact_address', $settings['contact_address'] ?? '123 Real Estate Ave, City, State 12345') }}">
                    </div>
                </div>
            </div>
        </div>

        <!-- Logo & Branding -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-image me-2"></i>Logo & Branding</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="logo" class="form-label">Website Logo</label>
                        @if(isset($settings['logo']) && $settings['logo'])
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['logo']) }}" alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                <div class="small text-muted mt-1">Current: {{ $settings['logo'] }}</div>
                            </div>
                        @endif
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                        <small class="form-text text-muted">Upload PNG, JPG, or SVG format</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="favicon" class="form-label">Favicon</label>
                        @if(isset($settings['favicon']) && $settings['favicon'])
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['favicon']) }}" alt="Current Favicon" class="img-thumbnail" style="max-height: 50px;">
                                <div class="small text-muted mt-1">Current: {{ $settings['favicon'] }}</div>
                            </div>
                        @endif
                        <input type="file" class="form-control" id="favicon" name="favicon" accept="image/*">
                        <small class="form-text text-muted">Upload ICO, PNG format (16x16 or 32x32)</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Theme Colors -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-palette me-2"></i>Theme Colors</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="primary_color" class="form-label">Primary Color</label>
                        <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                               value="{{ old('primary_color', $settings['primary_color'] ?? '#f8c146') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="secondary_color" class="form-label">Secondary Color</label>
                        <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" 
                               value="{{ old('secondary_color', $settings['secondary_color'] ?? '#8b751d') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="accent_color" class="form-label">Accent Color</label>
                        <input type="color" class="form-control form-control-color" id="accent_color" name="accent_color" 
                               value="{{ old('accent_color', $settings['accent_color'] ?? '#007bff') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="text_color" class="form-label">Text Color</label>
                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color" 
                               value="{{ old('text_color', $settings['text_color'] ?? '#333333') }}">
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>Social Media</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="social_facebook" class="form-label">Facebook URL</label>
                        <input type="url" class="form-control" id="social_facebook" name="social_facebook"
                               value="{{ old('social_facebook', $settings['social_facebook'] ?? '') }}" placeholder="https://facebook.com/yourpage">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="social_twitter" class="form-label">Twitter URL</label>
                        <input type="url" class="form-control" id="social_twitter" name="social_twitter"
                               value="{{ old('social_twitter', $settings['social_twitter'] ?? '') }}" placeholder="https://twitter.com/yourhandle">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="social_instagram" class="form-label">Instagram URL</label>
                        <input type="url" class="form-control" id="social_instagram" name="social_instagram"
                               value="{{ old('social_instagram', $settings['social_instagram'] ?? '') }}" placeholder="https://instagram.com/yourhandle">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="social_linkedin" class="form-label">LinkedIn URL</label>
                        <input type="url" class="form-control" id="social_linkedin" name="social_linkedin"
                               value="{{ old('social_linkedin', $settings['social_linkedin'] ?? '') }}" placeholder="https://linkedin.com/company/yourcompany">
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Text -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-copyright me-2"></i>Footer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <label for="footer_text" class="form-label">Footer Copyright Text</label>
                        <input type="text" class="form-control" id="footer_text" name="footer_text"
                               value="{{ old('footer_text', $settings['footer_text'] ?? '© 2024 Hestia Abodes. All rights reserved.') }}">
                    </div>
                    <div class="col-12 mb-3">
                        <label for="legal_disclaimer" class="form-label">Legal Disclaimer</label>
                        <textarea class="form-control" id="legal_disclaimer" name="legal_disclaimer" rows="3"
                                  placeholder="Legal disclaimer text for footer">{{ old('legal_disclaimer', $settings['legal_disclaimer'] ?? '') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        Save All Settings
                    </button>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="clearCache()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Clear Cache
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        fetch('{{ route("admin.settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache cleared successfully!');
                location.reload();
            } else {
                alert('Error clearing cache: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error clearing cache');
        });
    }
}
</script>
@endsection
