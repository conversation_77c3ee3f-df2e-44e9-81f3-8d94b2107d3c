@extends('admin.layouts.app')

@section('title', 'Image Manager')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Image Manager</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload Images
        </button>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Images by Page -->
    @if(count($imagesByPage) > 0)
        @foreach($imagesByPage as $pageName => $images)
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary text-capitalize">{{ str_replace('-', ' ', $pageName) }} Page Images</h6>
                <span class="badge bg-info">{{ count($images) }} images</span>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($images as $image)
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                        <div class="card h-100">
                            <img src="{{ asset($image['path']) }}" class="card-img-top" alt="{{ $image['name'] }}" 
                                 style="height: 150px; object-fit: cover; cursor: pointer;" 
                                 onclick="previewImage('{{ asset($image['path']) }}', '{{ $image['name'] }}')">
                            <div class="card-body p-2">
                                <h6 class="card-title small mb-1">{{ $image['name'] }}</h6>
                                <p class="card-text small text-muted mb-2">
                                    Size: {{ number_format($image['size'] / 1024, 1) }} KB<br>
                                    Modified: {{ date('M j, Y', $image['modified']) }}
                                </p>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-sm btn-warning" 
                                            onclick="replaceImage('{{ $image['path'] }}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" 
                                            onclick="deleteImage('{{ $image['path'] }}', '{{ $image['name'] }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endforeach
    @else
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Images Found</h5>
                <p class="text-muted">Upload some images to get started.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-upload"></i> Upload Images
                </button>
            </div>
        </div>
    @endif
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Images</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.images.upload') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="page" class="form-label">Select Page</label>
                        <select class="form-select" id="page" name="page" required>
                            <option value="">Choose a page...</option>
                            <option value="home">Home</option>
                            <option value="about">About Us</option>
                            <option value="services">Services</option>
                            <option value="contact">Contact</option>
                            <option value="projects">Projects</option>
                            <option value="blog">Blog</option>
                            <option value="media-center">Media Center</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="images" class="form-label">Select Images</label>
                        <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*" required>
                        <small class="form-text text-muted">You can select multiple images. Max size: 2MB per image.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Images</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewTitle">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<!-- Replace Image Modal -->
<div class="modal fade" id="replaceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Replace Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.images.replace') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" id="oldImagePath" name="old_image_path">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newImage" class="form-label">Select New Image</label>
                        <input type="file" class="form-control" id="newImage" name="new_image" accept="image/*" required>
                        <small class="form-text text-muted">Max size: 2MB</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Replace Image</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteImageName"></strong>?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.images.delete') }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" id="deleteImagePath" name="image_path">
                    <button type="submit" class="btn btn-danger">Delete Image</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(src, name) {
    document.getElementById('previewImage').src = src;
    document.getElementById('previewTitle').textContent = name;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function replaceImage(imagePath) {
    document.getElementById('oldImagePath').value = imagePath;
    new bootstrap.Modal(document.getElementById('replaceModal')).show();
}

function deleteImage(imagePath, imageName) {
    document.getElementById('deleteImagePath').value = imagePath;
    document.getElementById('deleteImageName').textContent = imageName;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endsection
