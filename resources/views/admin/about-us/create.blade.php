@extends('admin.layouts.app')

@section('title', 'Create About Us Section')
@section('page-title', 'Add New About Us Section')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Section Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.about-us.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title') }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" value="{{ old('subtitle') }}">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="8" required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="content_type" class="form-label">Content Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('content_type') is-invalid @enderror" 
                                        id="content_type" name="content_type" required>
                                    <option value="">Select Content Type</option>
                                    <option value="text" {{ old('content_type') === 'text' ? 'selected' : '' }}>Text Only</option>
                                    <option value="icon" {{ old('content_type') === 'icon' ? 'selected' : '' }}>With Icon</option>
                                    <option value="image" {{ old('content_type') === 'image' ? 'selected' : '' }}>With Image</option>
                                </select>
                                @error('content_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('is_active') is-invalid @enderror" id="is_active" name="is_active" required>
                                    <option value="1" {{ old('is_active', '1') == '1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>Inactive</option>
                                </select>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Icon Selection (Hidden by default) -->
                    <div id="icon-section" class="mb-3" style="display: none;">
                        <label for="icon_class" class="form-label">Icon Class</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('icon_class') is-invalid @enderror" 
                                   id="icon_class" name="icon_class" value="{{ old('icon_class') }}" 
                                   placeholder="e.g., fas fa-home, fas fa-shield-alt">
                            <button type="button" class="btn btn-outline-secondary" id="icon-picker-btn">
                                <i class="fas fa-icons"></i> Choose Icon
                            </button>
                        </div>
                        <div class="form-text">Popular icons: fas fa-home, fas fa-shield-alt, fas fa-lightbulb, fas fa-bullseye, fas fa-heart, fas fa-users</div>
                        @error('icon_class')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Image Upload (Hidden by default) -->
                    <div id="image-section" class="mb-3" style="display: none;">
                        <label for="image" class="form-label">Section Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        <div class="form-text">Recommended size: 800x600px. Max file size: 2MB</div>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.about-us.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Section
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Section Preview</h5>
            </div>
            <div class="card-body">
                <div id="preview-content">
                    <p class="text-muted">Fill in the form to see a preview of your section.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Popular Icons</h5>
            </div>
            <div class="card-body">
                <div class="row g-2" id="popular-icons">
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-home">
                            <i class="fas fa-home"></i><br><small>Home</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-shield-alt">
                            <i class="fas fa-shield-alt"></i><br><small>Shield</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-lightbulb">
                            <i class="fas fa-lightbulb"></i><br><small>Idea</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-bullseye">
                            <i class="fas fa-bullseye"></i><br><small>Target</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-heart">
                            <i class="fas fa-heart"></i><br><small>Heart</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-users">
                            <i class="fas fa-users"></i><br><small>Users</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- CKEditor -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#description'))
        .catch(error => {
            console.error(error);
        });
    
    // Content type change handler
    const contentTypeSelect = document.getElementById('content_type');
    const iconSection = document.getElementById('icon-section');
    const imageSection = document.getElementById('image-section');
    
    contentTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        
        // Hide all sections first
        iconSection.style.display = 'none';
        imageSection.style.display = 'none';
        
        // Show relevant section
        if (selectedType === 'icon') {
            iconSection.style.display = 'block';
        } else if (selectedType === 'image') {
            imageSection.style.display = 'block';
        }
    });
    
    // Icon selection
    document.querySelectorAll('.icon-select').forEach(button => {
        button.addEventListener('click', function() {
            const iconClass = this.getAttribute('data-icon');
            document.getElementById('icon_class').value = iconClass;
            
            // Update button states
            document.querySelectorAll('.icon-select').forEach(btn => btn.classList.remove('btn-primary'));
            this.classList.add('btn-primary');
        });
    });
    
    // Trigger change event on page load to show correct section
    contentTypeSelect.dispatchEvent(new Event('change'));
});
</script>
@endsection
