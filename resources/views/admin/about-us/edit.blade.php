@extends('admin.layouts.app')

@section('title', 'Edit About Us Section')
@section('page-title', 'Edit About Us Section')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Edit Section Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.about-us.update', $aboutUsSection) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $aboutUsSection->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $aboutUsSection->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" value="{{ old('subtitle', $aboutUsSection->subtitle) }}">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="8" required>{{ old('description', $aboutUsSection->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="content_type" class="form-label">Content Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('content_type') is-invalid @enderror" 
                                        id="content_type" name="content_type" required>
                                    <option value="">Select Content Type</option>
                                    <option value="text" {{ old('content_type', $aboutUsSection->content_type) === 'text' ? 'selected' : '' }}>Text Only</option>
                                    <option value="icon" {{ old('content_type', $aboutUsSection->content_type) === 'icon' ? 'selected' : '' }}>With Icon</option>
                                    <option value="image" {{ old('content_type', $aboutUsSection->content_type) === 'image' ? 'selected' : '' }}>With Image</option>
                                </select>
                                @error('content_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('is_active') is-invalid @enderror" id="is_active" name="is_active" required>
                                    <option value="1" {{ old('is_active', $aboutUsSection->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ old('is_active', $aboutUsSection->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                </select>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Icon Selection -->
                    <div id="icon-section" class="mb-3" style="display: none;">
                        <label for="icon_class" class="form-label">Icon Class</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('icon_class') is-invalid @enderror" 
                                   id="icon_class" name="icon_class" value="{{ old('icon_class', $aboutUsSection->icon_class) }}" 
                                   placeholder="e.g., fas fa-home, fas fa-shield-alt">
                            <button type="button" class="btn btn-outline-secondary" id="icon-picker-btn">
                                <i class="fas fa-icons"></i> Choose Icon
                            </button>
                        </div>
                        <div class="form-text">Popular icons: fas fa-home, fas fa-shield-alt, fas fa-lightbulb, fas fa-bullseye, fas fa-heart, fas fa-users</div>
                        @error('icon_class')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Image Upload -->
                    <div id="image-section" class="mb-3" style="display: none;">
                        <label for="image" class="form-label">Section Image</label>
                        
                        @if($aboutUsSection->hasImage())
                            <div class="mb-2">
                                <img src="{{ $aboutUsSection->image_url }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                <p class="text-muted small">Current image</p>
                            </div>
                        @endif
                        
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        <div class="form-text">
                            @if($aboutUsSection->hasImage())
                                Leave empty to keep current image.
                            @endif
                            Recommended size: 800x600px. Max file size: 2MB
                        </div>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror

                        <!-- Image Position -->
                        <div class="mt-3">
                            <label for="image_position" class="form-label">Image Position</label>
                            <select class="form-select @error('image_position') is-invalid @enderror"
                                    id="image_position" name="image_position">
                                <option value="left" {{ old('image_position', $aboutUsSection->image_position) === 'left' ? 'selected' : '' }}>Left of Text</option>
                                <option value="right" {{ old('image_position', $aboutUsSection->image_position) === 'right' ? 'selected' : '' }}>Right of Text</option>
                                <option value="top" {{ old('image_position', $aboutUsSection->image_position) === 'top' ? 'selected' : '' }}>Above Text</option>
                                <option value="bottom" {{ old('image_position', $aboutUsSection->image_position) === 'bottom' ? 'selected' : '' }}>Below Text</option>
                            </select>
                            @error('image_position')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Subcontent Section -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="columns_count" class="form-label">Number of Columns</label>
                                <select class="form-select @error('columns_count') is-invalid @enderror"
                                        id="columns_count" name="columns_count">
                                    <option value="1" {{ old('columns_count', $aboutUsSection->columns_count) == 1 ? 'selected' : '' }}>1 Column</option>
                                    <option value="2" {{ old('columns_count', $aboutUsSection->columns_count) == 2 ? 'selected' : '' }}>2 Columns</option>
                                    <option value="3" {{ old('columns_count', $aboutUsSection->columns_count) == 3 ? 'selected' : '' }}>3 Columns</option>
                                    <option value="4" {{ old('columns_count', $aboutUsSection->columns_count) == 4 ? 'selected' : '' }}>4 Columns</option>
                                </select>
                                @error('columns_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Subcontent Fields -->
                    <div id="subcontent-section" class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6>Subcontent Items</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-subcontent">
                                <i class="fas fa-plus me-1"></i>Add Item
                            </button>
                        </div>
                        <div id="subcontent-container">
                            <!-- Existing subcontent items will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.about-us.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.about-us.show', $aboutUsSection) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Section
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Current Section</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    @if($aboutUsSection->hasImage())
                        <img src="{{ $aboutUsSection->image_url }}" alt="{{ $aboutUsSection->title }}" class="img-fluid rounded">
                    @elseif($aboutUsSection->hasIcon())
                        <div class="p-4 bg-light rounded">
                            <i class="{{ $aboutUsSection->icon_class }} fa-3x text-primary"></i>
                        </div>
                    @endif
                </div>
                
                <h6>{{ $aboutUsSection->title }}</h6>
                @if($aboutUsSection->subtitle)
                    <p class="text-muted">{{ $aboutUsSection->subtitle }}</p>
                @endif
                <div class="small">
                    {!! Str::limit(strip_tags($aboutUsSection->description), 150) !!}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Popular Icons</h5>
            </div>
            <div class="card-body">
                <div class="row g-2" id="popular-icons">
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-home">
                            <i class="fas fa-home"></i><br><small>Home</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-shield-alt">
                            <i class="fas fa-shield-alt"></i><br><small>Shield</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-lightbulb">
                            <i class="fas fa-lightbulb"></i><br><small>Idea</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-bullseye">
                            <i class="fas fa-bullseye"></i><br><small>Target</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-heart">
                            <i class="fas fa-heart"></i><br><small>Heart</small>
                        </button>
                    </div>
                    <div class="col-4 text-center">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100 icon-select" data-icon="fas fa-users">
                            <i class="fas fa-users"></i><br><small>Users</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- CKEditor -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#description'))
        .catch(error => {
            console.error(error);
        });
    
    // Content type change handler
    const contentTypeSelect = document.getElementById('content_type');
    const iconSection = document.getElementById('icon-section');
    const imageSection = document.getElementById('image-section');
    
    contentTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        
        // Hide all sections first
        iconSection.style.display = 'none';
        imageSection.style.display = 'none';
        
        // Show relevant section
        if (selectedType === 'icon') {
            iconSection.style.display = 'block';
        } else if (selectedType === 'image') {
            imageSection.style.display = 'block';
        }
    });
    
    // Icon selection
    document.querySelectorAll('.icon-select').forEach(button => {
        button.addEventListener('click', function() {
            const iconClass = this.getAttribute('data-icon');
            document.getElementById('icon_class').value = iconClass;
            
            // Update button states
            document.querySelectorAll('.icon-select').forEach(btn => btn.classList.remove('btn-primary'));
            this.classList.add('btn-primary');
        });
    });
    
    // Trigger change event on page load to show correct section
    contentTypeSelect.dispatchEvent(new Event('change'));
    
    // Highlight current icon if it matches one of the popular icons
    const currentIcon = document.getElementById('icon_class').value;
    if (currentIcon) {
        document.querySelectorAll('.icon-select').forEach(button => {
            if (button.getAttribute('data-icon') === currentIcon) {
                button.classList.add('btn-primary');
            }
        });
    }

    // Subcontent management
    let subcontentIndex = 0;

    function addSubcontentItem(title = '', description = '', iconClass = '') {
        const container = document.getElementById('subcontent-container');
        const itemHtml = `
            <div class="card mb-3 subcontent-item" data-index="${subcontentIndex}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Item ${subcontentIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-subcontent">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="subcontent[${subcontentIndex}][title]" value="${title}" placeholder="Enter title">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Icon Class</label>
                                <input type="text" class="form-control" name="subcontent[${subcontentIndex}][icon_class]" value="${iconClass}" placeholder="e.g., fas fa-home">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="subcontent[${subcontentIndex}][description]" rows="3" placeholder="Enter description">${description}</textarea>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', itemHtml);
        subcontentIndex++;
    }

    // Add subcontent item
    document.getElementById('add-subcontent').addEventListener('click', function() {
        addSubcontentItem();
    });

    // Remove subcontent item
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-subcontent')) {
            e.target.closest('.subcontent-item').remove();
        }
    });

    // Load existing subcontent
    @if($aboutUsSection->subcontent)
        @foreach($aboutUsSection->subcontent as $item)
            addSubcontentItem(
                '{{ addslashes($item['title'] ?? '') }}',
                '{{ addslashes($item['description'] ?? '') }}',
                '{{ addslashes($item['icon_class'] ?? '') }}'
            );
        @endforeach
    @else
        // Add initial subcontent item if none exist
        addSubcontentItem();
    @endif
});
</script>
@endsection
