@extends('admin.layouts.app')

@section('title', 'Edit Page - ' . $page->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Edit Page: {{ $page->name }}</h1>
                <div>
                    <a href="{{ $page->url }}" target="_blank" class="btn btn-info me-2">
                        <i class="fas fa-external-link-alt"></i> View Page
                    </a>
                    <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Pages
                    </a>
                </div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('admin.pages.update', $page) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Basic Information Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Page Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="{{ old('name', $page->name) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">URL Slug</label>
                                    <input type="text" class="form-control" id="slug" name="slug"
                                           value="{{ old('slug', $page->slug) }}" placeholder="Auto-generated from name">
                                    <small class="form-text text-muted">Leave empty to auto-generate from page name</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Page Title (SEO) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ old('title', $page->title) }}" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template" class="form-label">Template <span class="text-danger">*</span></label>
                                    <select class="form-control" id="template" name="template" required>
                                        <option value="default" {{ old('template', $page->template) == 'default' ? 'selected' : '' }}>Default</option>
                                        <option value="about" {{ old('template', $page->template) == 'about' ? 'selected' : '' }}>About</option>
                                        <option value="contact" {{ old('template', $page->template) == 'contact' ? 'selected' : '' }}>Contact</option>
                                        <option value="services" {{ old('template', $page->template) == 'services' ? 'selected' : '' }}>Services</option>
                                        <option value="blog" {{ old('template', $page->template) == 'blog' ? 'selected' : '' }}>Blog</option>
                                        <option value="media" {{ old('template', $page->template) == 'media' ? 'selected' : '' }}>Media Center</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order"
                                           value="{{ old('sort_order', $page->sort_order) }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                               value="1" {{ old('is_active', $page->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control" id="meta_description" name="meta_description"
                                      rows="3" placeholder="SEO meta description">{{ old('meta_description', $page->meta_description) }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords"
                                   value="{{ old('meta_keywords', $page->meta_keywords) }}" placeholder="keyword1, keyword2, keyword3">
                        </div>
                    </div>
                </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template" class="form-label">Template <span class="text-danger">*</span></label>
                                    <select class="form-control" id="template" name="template" required>
                                        <option value="default" {{ old('template', $page->template) == 'default' ? 'selected' : '' }}>Default</option>
                                        <option value="about" {{ old('template', $page->template) == 'about' ? 'selected' : '' }}>About</option>
                                        <option value="contact" {{ old('template', $page->template) == 'contact' ? 'selected' : '' }}>Contact</option>
                                        <option value="services" {{ old('template', $page->template) == 'services' ? 'selected' : '' }}>Services</option>
                                        <option value="blog" {{ old('template', $page->template) == 'blog' ? 'selected' : '' }}>Blog</option>
                                        <option value="media" {{ old('template', $page->template) == 'media' ? 'selected' : '' }}>Media Center</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order"
                                           value="{{ old('sort_order', $page->sort_order) }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                               value="1" {{ old('is_active', $page->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control" id="meta_description" name="meta_description"
                                      rows="3" placeholder="SEO meta description">{{ old('meta_description', $page->meta_description) }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords"
                                   value="{{ old('meta_keywords', $page->meta_keywords) }}" placeholder="keyword1, keyword2, keyword3">
                        </div>
                    </div>
                </div>

                <!-- Main Content Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Main Content</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="content" class="form-label">Page Content</label>
                            <textarea class="form-control ckeditor" id="content" name="content"
                                      rows="8">{{ old('content', $page->content) }}</textarea>
                            <small class="form-text text-muted">Rich text editor for main page content</small>
                        </div>
                    </div>
                </div>

                <!-- Easy Section Management -->
                @php
                    $sections = $page->sections ?? [];
                    $heroSection = collect($sections)->firstWhere('type', 'hero') ?? [];
                    $statsSection = collect($sections)->firstWhere('type', 'stats') ?? [];
                    $teamSection = collect($sections)->firstWhere('type', 'team') ?? [];
                    $servicesSection = collect($sections)->firstWhere('type', 'services_grid') ?? [];
                    $contactSection = collect($sections)->firstWhere('type', 'contact_info') ?? [];
                @endphp

                <!-- Hero Section -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Hero Section</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hero_title" class="form-label">Hero Title</label>
                                    <input type="text" class="form-control" id="hero_title" name="hero_title"
                                           value="{{ old('hero_title', $heroSection['title'] ?? '') }}" placeholder="Main hero title">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hero_subtitle" class="form-label">Hero Subtitle</label>
                                    <input type="text" class="form-control" id="hero_subtitle" name="hero_subtitle"
                                           value="{{ old('hero_subtitle', $heroSection['subtitle'] ?? '') }}" placeholder="Hero subtitle">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="hero_description" class="form-label">Hero Description</label>
                            <textarea class="form-control" id="hero_description" name="hero_description"
                                      rows="3" placeholder="Hero section description">{{ old('hero_description', $heroSection['description'] ?? '') }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hero_image" class="form-label">Hero Image</label>
                                    <input type="file" class="form-control" id="hero_image" name="hero_image" accept="image/*">
                                    @if(isset($heroSection['image']) && $heroSection['image'])
                                        <small class="form-text text-muted">Current: {{ $heroSection['image'] }}</small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="hero_button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control" id="hero_button_text" name="hero_button_text"
                                           value="{{ old('hero_button_text', $heroSection['button_text'] ?? '') }}" placeholder="Learn More">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="hero_button_link" class="form-label">Button Link</label>
                                    <input type="text" class="form-control" id="hero_button_link" name="hero_button_link"
                                           value="{{ old('hero_button_link', $heroSection['button_link'] ?? '') }}" placeholder="#section">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                @if($page->template == 'contact')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone"
                                           value="{{ old('contact_phone', $contactSection['phone'] ?? '') }}" placeholder="+91 9876543210">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email"
                                           value="{{ old('contact_email', $contactSection['email'] ?? '') }}" placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="contact_address" class="form-label">Address</label>
                            <textarea class="form-control" id="contact_address" name="contact_address"
                                      rows="2" placeholder="Complete address">{{ old('contact_address', $contactSection['address'] ?? '') }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="contact_hours" class="form-label">Working Hours</label>
                            <input type="text" class="form-control" id="contact_hours" name="contact_hours"
                                   value="{{ old('contact_hours', $contactSection['working_hours'] ?? '') }}" placeholder="Mon - Sat: 9:00 AM - 7:00 PM">
                        </div>
                    </div>
                </div>
                @endif

                <!-- Services Section -->
                @if($page->template == 'services')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Services Content</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="services_title" class="form-label">Services Section Title</label>
                            <input type="text" class="form-control" id="services_title" name="services_title"
                                   value="{{ old('services_title', $servicesSection['title'] ?? '') }}" placeholder="What We Offer">
                        </div>

                        <div class="mb-3">
                            <label for="services_description" class="form-label">Services Description</label>
                            <textarea class="form-control ckeditor" id="services_description" name="services_description"
                                      rows="4" placeholder="Description of your services">{{ old('services_description', $servicesSection['description'] ?? '') }}</textarea>
                        </div>
                    </div>
                </div>
                @endif

                <!-- About Page Specific Sections -->
                @if($page->template == 'about')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Company Statistics</h6>
                    </div>
                    <div class="card-body">
                        @php
                            $stats = $statsSection['stats'] ?? [
                                ['number' => '500+', 'label' => 'Happy Clients'],
                                ['number' => '100+', 'label' => 'Projects Completed'],
                                ['number' => '15+', 'label' => 'Years Experience'],
                                ['number' => '50+', 'label' => 'Expert Team']
                            ];
                        @endphp

                        <div class="row">
                            @foreach($stats as $index => $stat)
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Stat {{ $index + 1 }} Number</label>
                                    <input type="text" class="form-control" name="stats[{{ $index }}][number]"
                                           value="{{ old('stats.'.$index.'.number', $stat['number'] ?? '') }}" placeholder="500+">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Stat {{ $index + 1 }} Label</label>
                                    <input type="text" class="form-control" name="stats[{{ $index }}][label]"
                                           value="{{ old('stats.'.$index.'.label', $stat['label'] ?? '') }}" placeholder="Happy Clients">
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Additional Images Section -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Additional Images</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="gallery_images" class="form-label">Upload Images</label>
                            <input type="file" class="form-control" id="gallery_images" name="gallery_images[]"
                                   multiple accept="image/*">
                            <small class="form-text text-muted">Select multiple images for the page gallery</small>
                        </div>

                        <!-- Display existing images if any -->
                        @if($page->images && count($page->images) > 0)
                        <div class="mt-3">
                            <label class="form-label">Current Images:</label>
                            <div class="row">
                                @foreach($page->images as $image)
                                <div class="col-md-3 mb-2">
                                    <div class="card">
                                        <img src="{{ asset('storage/' . $image) }}" class="card-img-top" style="height: 150px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" name="remove_images[]" value="{{ $image }}">
                                                <label class="form-check-label small">Remove</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Page
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- CKEditor CDN -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

<script>
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});

// Initialize CKEditor for all textareas with ckeditor class
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.ckeditor').forEach(function(element) {
        ClassicEditor
            .create(element, {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'link', '|',
                        'bulletedList', 'numberedList', '|',
                        'outdent', 'indent', '|',
                        'blockQuote', 'insertTable', '|',
                        'undo', 'redo'
                    ]
                },
                language: 'en',
                table: {
                    contentToolbar: [
                        'tableColumn',
                        'tableRow',
                        'mergeTableCells'
                    ]
                }
            })
            .catch(error => {
                console.error(error);
            });
    });
});
</script>
@endsection
