@extends('admin.layouts.app')

@section('title', 'Edit Page - ' . $page->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit"></i> Edit Page - {{ $page->name }}
        </h1>
        <div>
            <a href="{{ $page->url }}" target="_blank" class="btn btn-info btn-sm me-2">
                <i class="fas fa-external-link-alt"></i> View Page
            </a>
            <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Pages
            </a>
            <div class="btn-group">
                <a href="{{ route('admin.pages.sections.create', $page) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Section
                </a>
                <button type="button" class="btn btn-primary btn-sm dropdown-toggle dropdown-toggle-split"
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><h6 class="dropdown-header">Quick Add Section Types</h6></li>
                    @php
                        $sectionTypes = [
                            'hero' => 'Hero Section',
                            'about' => 'About Section',
                            'services' => 'Services Section',
                            'features' => 'Features Section',
                            'testimonials' => 'Testimonials Section',
                            'gallery' => 'Gallery Section',
                            'contact' => 'Contact Section',
                            'team' => 'Team Section',
                            'stats' => 'Statistics Section',
                            'cta' => 'Call to Action',
                            'custom' => 'Custom Section'
                        ];
                    @endphp
                    @foreach($sectionTypes as $key => $label)
                        <li>
                            <a class="dropdown-item" href="{{ route('admin.pages.sections.create', $page) }}?type={{ $key }}">
                                <i class="fas fa-{{ $key == 'hero' ? 'star' : ($key == 'about' ? 'info-circle' : ($key == 'services' ? 'cogs' : ($key == 'features' ? 'list-ul' : ($key == 'testimonials' ? 'quote-left' : ($key == 'gallery' ? 'images' : ($key == 'contact' ? 'envelope' : ($key == 'team' ? 'users' : ($key == 'stats' ? 'chart-bar' : ($key == 'cta' ? 'bullhorn' : 'puzzle-piece'))))))))) }} me-2"></i>
                                {{ $label }}
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Page Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> Page Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Page Name:</strong> {{ $page->name }}
                        </div>
                        <div class="col-md-6">
                            <strong>URL:</strong> <a href="{{ $page->url }}" target="_blank">{{ $page->url }}</a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>Status:</strong>
                            <span class="badge bg-{{ $page->is_active ? 'success' : 'warning' }}">
                                {{ $page->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="col-md-6">
                            <strong>Last Updated:</strong> {{ $page->updated_at->format('M d, Y H:i') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i> Section Statistics
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-primary">{{ $page->pageSections()->count() }}</h3>
                    <p class="mb-0">Total Sections</p>
                    <small class="text-muted">
                        {{ $page->pageSections()->where('is_active', true)->count() }} Active,
                        {{ $page->pageSections()->where('is_active', false)->count() }} Inactive
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Sections Management -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-puzzle-piece"></i> Page Sections
            </h6>
        </div>
        <div class="card-body">
            @php
                $sections = $page->pageSections()->ordered()->get();
                $sectionTypes = [
                    'hero' => 'Hero Section',
                    'about' => 'About Section',
                    'services' => 'Services Section',
                    'features' => 'Features Section',
                    'testimonials' => 'Testimonials Section',
                    'gallery' => 'Gallery Section',
                    'contact' => 'Contact Section',
                    'team' => 'Team Section',
                    'stats' => 'Statistics Section',
                    'cta' => 'Call to Action',
                    'faq' => 'FAQ Section',
                    'blog' => 'Blog Section',
                    'custom' => 'Custom Section'
                ];
            @endphp

            @if($sections->count() > 0)
                <div class="mb-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Section Management:</strong> Drag sections to reorder them. Each section is numbered for easy reference.
                        You can add new sections, edit existing ones, or modify their content including icons, images, and buttons.
                    </div>
                </div>

                <div id="sections-container">
                    @foreach($sections as $index => $section)
                    <div class="card mb-3 section-item" data-id="{{ $section->id }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-grip-vertical text-muted me-2 drag-handle" style="cursor: move;"></i>
                                <div class="section-number-badge me-2">
                                    <span class="badge bg-dark">{{ $index + 1 }}</span>
                                </div>
                                <div>
                                    <h6 class="mb-0">
                                        <span class="badge bg-info me-2">{{ $sectionTypes[$section->section_type] ?? $section->section_type }}</span>
                                        {{ $section->title ?: 'Untitled Section' }}
                                    </h6>
                                    @if($section->subtitle)
                                        <small class="text-muted">{{ Str::limit($section->subtitle, 50) }}</small>
                                    @endif
                                </div>
                                @if(!$section->is_active)
                                    <span class="badge bg-warning ms-2">Inactive</span>
                                @endif
                            </div>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.pages.sections.edit', [$page, $section]) }}"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteSection({{ $section->id }})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <!-- Section Details -->
                                    <div class="section-details mb-3">
                                        @if($section->icon)
                                            <div class="mb-2">
                                                <i class="{{ $section->icon_type ?? 'fas' }} {{ strpos($section->icon, 'fa-') === 0 ? $section->icon : 'fa-' . $section->icon }} text-primary me-2"></i>
                                                <small class="text-muted">Icon: {{ $section->icon }}</small>
                                            </div>
                                        @endif
                                        @if($section->button_text)
                                            <div class="mb-2">
                                                <span class="btn btn-sm btn-outline-primary me-2">{{ $section->button_text }}</span>
                                                @if($section->button_url)
                                                    <small class="text-muted">→ {{ $section->button_url }}</small>
                                                @endif
                                            </div>
                                        @endif
                                    </div>

                                    @if($section->subtitle)
                                        <p class="text-muted mb-2"><strong>Subtitle:</strong> {{ $section->subtitle }}</p>
                                    @endif
                                    @if($section->content)
                                        <div class="content-preview">
                                            <strong>Content:</strong> {!! \Str::limit(strip_tags($section->content), 150) !!}
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-4">
                                    @if($section->images && count($section->images) > 0)
                                        <div class="section-images">
                                            <small class="text-muted">Images ({{ count($section->images) }}):</small>
                                            <div class="d-flex flex-wrap mt-1">
                                                @foreach(array_slice($section->images, 0, 3) as $image)
                                                    <img src="{{ asset('storage/' . $image) }}"
                                                         class="img-thumbnail me-1 mb-1"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                @endforeach
                                                @if(count($section->images) > 3)
                                                    <div class="img-thumbnail d-flex align-items-center justify-content-center me-1 mb-1"
                                                         style="width: 50px; height: 50px; background-color: #f8f9fa;">
                                                        <small class="text-muted">+{{ count($section->images) - 3 }}</small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Add New Section Card -->
                <div class="card border-2 border-dashed border-primary bg-light">
                    <div class="card-body text-center py-4">
                        <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                        <h5 class="text-primary mb-3">Add New Section</h5>
                        <p class="text-muted mb-3">Expand your page content by adding more sections</p>
                        <div class="d-flex justify-content-center flex-wrap gap-2">
                            @foreach(array_slice($sectionTypes, 0, 6, true) as $key => $label)
                                <a href="{{ route('admin.pages.sections.create', $page) }}?type={{ $key }}"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-{{ $key == 'hero' ? 'star' : ($key == 'about' ? 'info-circle' : ($key == 'services' ? 'cogs' : ($key == 'features' ? 'list-ul' : ($key == 'testimonials' ? 'quote-left' : ($key == 'gallery' ? 'images' : 'puzzle-piece'))))) }} me-1"></i>
                                    {{ $label }}
                                </a>
                            @endforeach
                            <a href="{{ route('admin.pages.sections.create', $page) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i> Custom Section
                            </a>
                        </div>
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No sections found</h5>
                    <p class="text-muted">Start building your page by adding sections.</p>
                    <a href="{{ route('admin.pages.sections.create', $page) }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Section
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this section? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Section</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- SortableJS for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
let sectionToDelete = null;

// Initialize sortable
if (document.getElementById('sections-container')) {
    new Sortable(document.getElementById('sections-container'), {
        handle: '.drag-handle',
        animation: 150,
        onEnd: function(evt) {
            updateSectionOrder();
        }
    });
}

// Update section order
function updateSectionOrder() {
    const sections = document.querySelectorAll('.section-item');
    const orderData = [];

    sections.forEach((section, index) => {
        orderData.push({
            id: section.dataset.id,
            sort_order: index
        });

        // Update the number badge
        const badge = section.querySelector('.section-number-badge .badge');
        if (badge) {
            badge.textContent = index + 1;
        }
    });

    // Send AJAX request to update order
    fetch('{{ route("admin.pages.sections.reorder", $page) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ sections: orderData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            console.log('Section order updated successfully');
        }
    })
    .catch(error => {
        console.error('Error updating section order:', error);
    });
}

// Delete section
function deleteSection(sectionId) {
    sectionToDelete = sectionId;
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `{{ route('admin.pages.sections.index', $page) }}/${sectionId}`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Initialize sortable
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('sections-container');
    if (container) {
        new Sortable(container, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function(evt) {
                updateSectionOrder();
            }
        });
    }
});

// Update section order
function updateSectionOrder() {
    const sections = [];
    document.querySelectorAll('.section-item').forEach((item, index) => {
        sections.push({
            id: item.dataset.id,
            sort_order: index + 1
        });
    });

    fetch('{{ route("admin.pages.sections.reorder", $page) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ sections: sections })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update section numbers
            document.querySelectorAll('.section-number-badge .badge').forEach((badge, index) => {
                badge.textContent = index + 1;
            });
        }
    })
    .catch(error => {
        console.error('Error updating section order:', error);
    });
}
</script>

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
@endsection
