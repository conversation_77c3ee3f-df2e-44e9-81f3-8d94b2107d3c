@extends('admin.layouts.app')

@section('title', 'View Page - ' . $page->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Page Details: {{ $page->name }}</h1>
                <div>
                    <a href="{{ $page->url }}" target="_blank" class="btn btn-success me-2">
                        <i class="fas fa-external-link-alt"></i> View Live Page
                    </a>
                    <a href="{{ route('admin.pages.edit', $page) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit"></i> Edit Page
                    </a>
                    <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Pages
                    </a>
                </div>
            </div>

            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $page->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Slug:</strong></td>
                                    <td><code>{{ $page->slug }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Template:</strong></td>
                                    <td><span class="badge bg-info">{{ ucfirst($page->template) }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($page->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Sort Order:</strong></td>
                                    <td>{{ $page->sort_order }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $page->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Updated:</strong></td>
                                    <td>{{ $page->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>URL:</strong></td>
                                    <td>
                                        <a href="{{ $page->url }}" target="_blank" class="text-primary">
                                            {{ $page->url }} <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Page Title:</strong>
                        <p class="mb-2">{{ $page->title }}</p>
                    </div>
                    
                    @if($page->meta_description)
                    <div class="mb-3">
                        <strong>Meta Description:</strong>
                        <p class="mb-2">{{ $page->meta_description }}</p>
                    </div>
                    @endif
                    
                    @if($page->meta_keywords)
                    <div class="mb-3">
                        <strong>Meta Keywords:</strong>
                        <p class="mb-2">{{ $page->meta_keywords }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Page Content -->
            @if($page->content)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Page Content</h6>
                </div>
                <div class="card-body">
                    <div class="content-preview">
                        {!! $page->content !!}
                    </div>
                </div>
            </div>
            @endif

            <!-- Page Sections -->
            @if($page->sections && count($page->sections) > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Page Sections ({{ count($page->sections) }})</h6>
                </div>
                <div class="card-body">
                    @foreach($page->sections as $index => $section)
                    <div class="section-item border rounded p-4 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                Section {{ $index + 1 }}: {{ $section['title'] ?? 'Untitled Section' }}
                            </h5>
                            <div>
                                <span class="badge bg-primary">{{ $section['type'] ?? 'Unknown' }}</span>
                                @if(isset($section['key']))
                                    <span class="badge bg-secondary">{{ $section['key'] }}</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="row">
                            @if(isset($section['subtitle']))
                            <div class="col-md-6">
                                <strong>Subtitle:</strong>
                                <p>{{ $section['subtitle'] }}</p>
                            </div>
                            @endif
                            
                            @if(isset($section['image']))
                            <div class="col-md-6">
                                <strong>Image:</strong>
                                <p><code>{{ $section['image'] }}</code></p>
                            </div>
                            @endif
                        </div>
                        
                        @if(isset($section['description']))
                        <div class="mb-3">
                            <strong>Description:</strong>
                            <p>{{ $section['description'] }}</p>
                        </div>
                        @endif
                        
                        @if(isset($section['button_text']) || isset($section['button_link']))
                        <div class="row">
                            @if(isset($section['button_text']))
                            <div class="col-md-6">
                                <strong>Button Text:</strong>
                                <p>{{ $section['button_text'] }}</p>
                            </div>
                            @endif
                            
                            @if(isset($section['button_link']))
                            <div class="col-md-6">
                                <strong>Button Link:</strong>
                                <p><code>{{ $section['button_link'] }}</code></p>
                            </div>
                            @endif
                        </div>
                        @endif
                        
                        <!-- Display additional section data -->
                        @php
                            $basicFields = ['type', 'key', 'title', 'subtitle', 'description', 'image', 'button_text', 'button_link'];
                            $additionalData = array_diff_key($section, array_flip($basicFields));
                        @endphp
                        
                        @if(count($additionalData) > 0)
                        <div class="mt-3">
                            <strong>Additional Data:</strong>
                            <div class="bg-light p-3 rounded mt-2">
                                <pre class="mb-0"><code>{{ json_encode($additionalData, JSON_PRETTY_PRINT) }}</code></pre>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.pages.edit', $page) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Page
                        </a>
                        <a href="{{ $page->url }}" target="_blank" class="btn btn-success">
                            <i class="fas fa-external-link-alt"></i> View Live Page
                        </a>
                        <form action="{{ route('admin.pages.destroy', $page) }}" method="POST" class="d-inline"
                              onsubmit="return confirm('Are you sure you want to delete this page?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete Page
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.content-preview {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    background-color: #f8f9fc;
}

.section-item {
    background-color: #f8f9fc;
}

pre code {
    font-size: 0.875rem;
    color: #5a5c69;
}
</style>
@endsection
