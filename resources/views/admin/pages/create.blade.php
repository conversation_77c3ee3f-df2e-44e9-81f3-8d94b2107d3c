@extends('admin.layouts.app')

@section('title', 'Create New Page')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Create New Page</h1>
                <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Pages
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Page Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.pages.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Page Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">URL Slug</label>
                                    <input type="text" class="form-control" id="slug" name="slug" 
                                           value="{{ old('slug') }}" placeholder="Auto-generated from name">
                                    <small class="form-text text-muted">Leave empty to auto-generate from page name</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Page Title (SEO) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="{{ old('title') }}" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template" class="form-label">Template <span class="text-danger">*</span></label>
                                    <select class="form-control" id="template" name="template" required>
                                        <option value="default" {{ old('template') == 'default' ? 'selected' : '' }}>Default</option>
                                        <option value="about" {{ old('template') == 'about' ? 'selected' : '' }}>About</option>
                                        <option value="contact" {{ old('template') == 'contact' ? 'selected' : '' }}>Contact</option>
                                        <option value="services" {{ old('template') == 'services' ? 'selected' : '' }}>Services</option>
                                        <option value="blog" {{ old('template') == 'blog' ? 'selected' : '' }}>Blog</option>
                                        <option value="media" {{ old('template') == 'media' ? 'selected' : '' }}>Media Center</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="{{ old('sort_order', 0) }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control" id="meta_description" name="meta_description" 
                                      rows="3" placeholder="SEO meta description">{{ old('meta_description') }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                                   value="{{ old('meta_keywords') }}" placeholder="keyword1, keyword2, keyword3">
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Page Content</label>
                            <textarea class="form-control" id="content" name="content"
                                      rows="6">{{ old('content') }}</textarea>
                            <small class="form-text text-muted">Basic HTML content for the page</small>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Sections Management -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Page Sections (Dynamic Content)</h6>
                        <button type="button" class="btn btn-sm btn-success" id="addSection">
                            <i class="fas fa-plus"></i> Add Section
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="sectionsContainer">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Section-wise Content Management:</strong> Add different types of sections like Hero, Content, Contact Info, Services, etc. Each section can be managed independently.
                            </div>
                        </div>

                        <!-- Section Template (Hidden) -->
                        <div id="sectionTemplate" style="display: none;">
                            <div class="section-item border rounded p-3 mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Section <span class="section-number">1</span></h6>
                                    <button type="button" class="btn btn-sm btn-danger remove-section">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Section Type</label>
                                            <select class="form-control section-type" name="sections[INDEX][type]">
                                                <option value="hero">Hero Section</option>
                                                <option value="content">Content Block</option>
                                                <option value="services">Services Grid</option>
                                                <option value="stats">Statistics</option>
                                                <option value="team">Team Members</option>
                                                <option value="contact_info">Contact Information</option>
                                                <option value="contact_form">Contact Form</option>
                                                <option value="gallery">Image Gallery</option>
                                                <option value="testimonials">Testimonials</option>
                                                <option value="custom">Custom Section</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Section Key</label>
                                            <input type="text" class="form-control" name="sections[INDEX][key]"
                                                   placeholder="unique_section_key">
                                            <small class="form-text text-muted">Unique identifier for this section</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Section Title</label>
                                            <input type="text" class="form-control" name="sections[INDEX][title]"
                                                   placeholder="Section Title">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Subtitle</label>
                                            <input type="text" class="form-control" name="sections[INDEX][subtitle]"
                                                   placeholder="Section Subtitle">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Image Path</label>
                                            <input type="text" class="form-control" name="sections[INDEX][image]"
                                                   placeholder="images/section-image.jpg">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" name="sections[INDEX][description]"
                                              rows="3" placeholder="Section description or content"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Button Text</label>
                                            <input type="text" class="form-control" name="sections[INDEX][button_text]"
                                                   placeholder="Learn More">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Button Link</label>
                                            <input type="text" class="form-control" name="sections[INDEX][button_link]"
                                                   placeholder="#section or /page-url">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Additional Data (JSON)</label>
                                    <textarea class="form-control" name="sections[INDEX][data]"
                                              rows="4" placeholder='{"key": "value", "items": []}'>{{ old('sections.INDEX.data') }}</textarea>
                                    <small class="form-text text-muted">For complex data like stats, team members, services list, etc. Use valid JSON format.</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Page
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let sectionIndex = 0;

document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});

// Add Section functionality
document.getElementById('addSection').addEventListener('click', function() {
    const template = document.getElementById('sectionTemplate');
    const container = document.getElementById('sectionsContainer');
    const clone = template.cloneNode(true);

    clone.style.display = 'block';
    clone.id = '';

    // Replace INDEX with actual index
    const html = clone.innerHTML.replace(/INDEX/g, sectionIndex);
    clone.innerHTML = html;

    // Update section number
    clone.querySelector('.section-number').textContent = sectionIndex + 1;

    container.appendChild(clone);
    sectionIndex++;

    // Add remove functionality
    clone.querySelector('.remove-section').addEventListener('click', function() {
        clone.remove();
        updateSectionNumbers();
    });
});

// Update section numbers after removal
function updateSectionNumbers() {
    const sections = document.querySelectorAll('.section-item:not(#sectionTemplate)');
    sections.forEach((section, index) => {
        section.querySelector('.section-number').textContent = index + 1;
    });
}

// Auto-generate section key based on type and title
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('section-type') || e.target.name && e.target.name.includes('[title]')) {
        const sectionItem = e.target.closest('.section-item');
        const typeSelect = sectionItem.querySelector('.section-type');
        const titleInput = sectionItem.querySelector('input[name*="[title]"]');
        const keyInput = sectionItem.querySelector('input[name*="[key]"]');

        if (typeSelect && titleInput && keyInput && !keyInput.value) {
            const type = typeSelect.value;
            const title = titleInput.value;

            let key = type;
            if (title) {
                key = title.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .substring(0, 20);
            }

            keyInput.value = key;
        }
    }
});
</script>
@endsection
