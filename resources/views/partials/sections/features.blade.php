<!-- Features Section -->
<section class="features-section py-5" @if($section->background_color) style="background-color: {{ $section->background_color }}" @endif>
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                @if($section->subtitle)
                    <span class="section-subtitle">{{ $section->subtitle }}</span>
                @endif
                <h2 class="section-title">{{ $section->title }}</h2>
                @if($section->content)
                    <p class="section-description">{{ $section->content }}</p>
                @endif
            </div>
        </div>
        
        @if($section->items && count($section->items) > 0)
            <div class="row">
                @foreach($section->items as $item)
                    @if($item['title'])
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card text-center">
                                @if($item['icon'])
                                    <div class="feature-icon mb-3">
                                        <i class="fas {{ $item['icon'] }}"></i>
                                    </div>
                                @endif
                                <h4 class="feature-title">{{ $item['title'] }}</h4>
                                @if($item['description'])
                                    <p class="feature-description">{{ $item['description'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        @endif
    </div>
</section>
