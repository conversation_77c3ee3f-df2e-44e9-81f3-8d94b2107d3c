<!-- Testimonials Section -->
<section class="testimonials-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <span class="section-subtitle">What Our Clients Say</span>
                <h2 class="section-title">Client Testimonials</h2>
                <p class="section-description">Real stories from real customers</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-12">
                <div class="testimonials-slider swiper">
                    <div class="swiper-wrapper">
                        @foreach($testimonials as $testimonial)
                            <div class="swiper-slide">
                                <div class="testimonial-card text-center">
                                    @if($testimonial->image)
                                        <div class="testimonial-avatar mb-3">
                                            <img src="{{ asset($testimonial->image) }}" alt="{{ $testimonial->name }}" class="rounded-circle">
                                        </div>
                                    @endif
                                    <div class="testimonial-content">
                                        <p class="testimonial-text">"{{ $testimonial->content }}"</p>
                                        <h5 class="testimonial-name">{{ $testimonial->name }}</h5>
                                        @if($testimonial->designation)
                                            <span class="testimonial-designation">{{ $testimonial->designation }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
    </div>
</section>
