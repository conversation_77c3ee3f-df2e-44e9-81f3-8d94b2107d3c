<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="about-content">
                    @if($section->subtitle)
                        <span class="section-subtitle">{{ $section->subtitle }}</span>
                    @endif
                    <h2 class="section-title mb-4">{{ $section->title }}</h2>
                    @if($section->content)
                        <div class="about-text mb-4">
                            {!! $section->content !!}
                        </div>
                    @endif
                    
                    @if($section->show_stats && $section->stats)
                        <div class="stats-row">
                            @foreach($section->stats as $stat)
                                @if($stat['number'] && $stat['label'])
                                    <div class="stat-item">
                                        <h3 class="stat-number">{{ $stat['number'] }}</h3>
                                        <p class="stat-label">{{ $stat['label'] }}</p>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endif
                    
                    @if($section->cta_text && $section->cta_url)
                        <a href="{{ $section->cta_url }}" class="btn btn-primary">{{ $section->cta_text }}</a>
                    @endif
                </div>
            </div>
            <div class="col-lg-6">
                @if($section->images && count($section->images) > 0)
                    <div class="about-image">
                        <img src="{{ asset($section->images[0]) }}" alt="{{ $section->title }}" class="img-fluid rounded">
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
