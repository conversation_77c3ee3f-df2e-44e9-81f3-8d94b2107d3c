<!-- Projects Section -->
<section class="projects-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                @if($section->subtitle)
                    <span class="section-subtitle">{{ $section->subtitle }}</span>
                @endif
                <h2 class="section-title">{{ $section->title }}</h2>
                @if($section->content)
                    <p class="section-description">{{ $section->content }}</p>
                @endif
            </div>
        </div>
        
        @if($section->items && count($section->items) > 0)
            <div class="row">
                @foreach($section->items as $item)
                    @if($item['title'])
                        <div class="col-lg-6 col-md-6 mb-4">
                            <div class="project-card">
                                @if($item['image'])
                                    <div class="project-image">
                                        <img src="{{ asset($item['image']) }}" alt="{{ $item['title'] }}" class="img-fluid">
                                    </div>
                                @endif
                                <div class="project-content p-4">
                                    @if($item['category'])
                                        <span class="project-category">{{ $item['category'] }}</span>
                                    @endif
                                    <h4 class="project-title">{{ $item['title'] }}</h4>
                                    @if($item['description'])
                                        <p class="project-description">{{ $item['description'] }}</p>
                                    @endif
                                    @if($item['url'])
                                        <a href="{{ $item['url'] }}" class="btn btn-outline-primary">View Details</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        @endif
        
        @if($section->cta_text && $section->cta_url)
            <div class="row">
                <div class="col-lg-12 text-center mt-4">
                    <a href="{{ $section->cta_url }}" class="btn btn-primary">{{ $section->cta_text }}</a>
                </div>
            </div>
        @endif
    </div>
</section>
