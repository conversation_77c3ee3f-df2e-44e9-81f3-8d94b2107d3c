<!-- Services Section -->
<section class="services-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                @if($section->subtitle)
                    <span class="section-subtitle">{{ $section->subtitle }}</span>
                @endif
                <h2 class="section-title">{{ $section->title }}</h2>
                @if($section->content)
                    <p class="section-description">{{ $section->content }}</p>
                @endif
            </div>
        </div>
        
        @if($section->items && count($section->items) > 0)
            <div class="row">
                @foreach($section->items as $item)
                    @if($item['title'])
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="service-card h-100">
                                <div class="service-content p-4">
                                    <h4 class="service-title mb-3">{{ $item['title'] }}</h4>
                                    @if($item['description'])
                                        <p class="service-description">{{ $item['description'] }}</p>
                                    @endif
                                    @if($item['url'])
                                        <a href="{{ $item['url'] }}" class="btn btn-outline-primary">Learn More</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        @endif
    </div>
</section>
