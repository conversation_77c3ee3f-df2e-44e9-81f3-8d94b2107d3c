<!-- Locations Section -->
<section class="locations-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                @if($section->subtitle)
                    <span class="section-subtitle">{{ $section->subtitle }}</span>
                @endif
                <h2 class="section-title">{{ $section->title }}</h2>
                @if($section->content)
                    <p class="section-description">{{ $section->content }}</p>
                @endif
            </div>
        </div>
        
        @if($section->items && count($section->items) > 0)
            <div class="row">
                @foreach($section->items as $item)
                    @if($item['title'])
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="location-card text-center">
                                @if($item['image'])
                                    <div class="location-image mb-3">
                                        <img src="{{ asset($item['image']) }}" alt="{{ $item['title'] }}" class="img-fluid rounded">
                                    </div>
                                @endif
                                <h4 class="location-title">{{ $item['title'] }}</h4>
                                @if($item['description'])
                                    <p class="location-description">{{ $item['description'] }}</p>
                                @endif
                                @if($item['url'])
                                    <a href="{{ $item['url'] }}" class="btn btn-outline-primary">Explore {{ $item['title'] }}</a>
                                @endif
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        @endif
    </div>
</section>
