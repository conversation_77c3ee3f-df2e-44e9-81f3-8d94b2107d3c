@extends('layouts.app')

@section('title', 'Contact Us - He<PERSON>ia <PERSON>')
@section('description', 'Get in touch with <PERSON><PERSON><PERSON>. Contact our real estate experts for personalized property recommendations and consultation.')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
.contact-card {
    background: white;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: 1px solid #f0f0f0;
    margin-bottom: 30px;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: #b8860b !important;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
    flex-shrink: 0;
    position: relative;
}

.contact-icon i {
    color: #ffffff !important;
    font-size: 28px;
    line-height: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.contact-card h4 {
    color: #333 !important;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 20px;
    line-height: 1.2;
}

.contact-card p {
    color: #666 !important;
    margin: 0;
    line-height: 1.6;
    font-size: 16px;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-card a {
    color: #cfaa13 !important;
    text-decoration: none;
    font-weight: 500;
}

.contact-card a:hover {
    color: #b8951a !important;
}

.section-padding {
    padding: 80px 0;
}

/* Ensure equal height columns */
.contact-info .row {
    display: flex;
    flex-wrap: wrap;
}

.contact-info .col-lg-3 {
    display: flex;
    margin-bottom: 30px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .contact-card {
        min-height: 250px;
        padding: 30px 15px;
    }

    .contact-icon {
        width: 70px;
        height: 70px;
        font-size: 24px;
    }

    .contact-icon i {
        font-size: 24px;
    }
}

.page-header {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('{{ asset('images/hero-bg.jpg') }}');
    background-size: cover;
    background-position: center;
    padding: 120px 0 80px;
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: white !important;
}

.page-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    color: white !important;
}

.breadcrumb {
    background: transparent;
    margin: 0;
}

.breadcrumb-item a {
    color: white !important;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: rgba(255,255,255,0.8) !important;
}

/* Quick Contact Cards */
.quick-contact-card {
    background: white;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: 1px solid #f0f0f0;
}

.quick-contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.quick-contact-card:hover .quick-contact-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(207, 170, 19, 0.3);
}

.quick-contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #cfaa13, #b8951a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
    color: white !important;
    transition: all 0.3s ease;
}

.quick-contact-card h4 {
    color: #333 !important;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 20px;
}

.quick-contact-card p {
    color: #666 !important;
    margin-bottom: 20px;
    line-height: 1.6;
}

.btn-success {
    background-color: #cfaa13 !important;
    border-color: #cfaa13 !important;
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: #b8951a !important;
    border-color: #b8951a !important;
    color: white !important;
    transform: translateY(-2px);
}

/* Contact Features */
.contact-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: #b8860b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.feature-content h5 {
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
}

.feature-content p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}

/* Map Container */
.map-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
}

.map-placeholder i {
    font-size: 48px;
    color: #b8860b;
    margin-bottom: 15px;
}

.map-placeholder h5 {
    color: #333;
    margin-bottom: 10px;
}

.map-placeholder p {
    color: #666;
    margin-bottom: 20px;
}

.btn-outline-primary {
    color: #b8860b;
    border-color: #b8860b;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #b8860b;
    border-color: #b8860b;
    color: white;
}
</style>
@endpush

@section('content')

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title">Let's Talk Property</h1>
                <p class="page-subtitle">Get in Touch with Our Real Estate Experts</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Contact Us</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="contact-info section-padding">
    <div class="container">
        <div class="row d-flex align-items-stretch">
            <div class="col-lg-3 col-md-6 mb-4 d-flex">
                <div class="contact-card w-100">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4>Visit Us</h4>
                    <p>{{ $settings['contact_address'] ?? 'Office No. 123, ABC Complex, Pune, Maharashtra 411001, India' }}</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 d-flex">
                <div class="contact-card w-100">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4>Call Us</h4>
                    <p><a href="tel:{{ str_replace(['+', ' ', '-'], '', $settings['contact_phone'] ?? '+************') }}">{{ $settings['contact_phone'] ?? '+91 98765 43210' }}</a></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 d-flex">
                <div class="contact-card w-100">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4>Email Us</h4>
                    <p><a href="mailto:{{ $settings['contact_email'] ?? '<EMAIL>' }}">{{ $settings['contact_email'] ?? '<EMAIL>' }}</a></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 d-flex">
                <div class="contact-card w-100">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>Working Hours</h4>
                    <p>{{ $settings['working_hours'] ?? 'Mon - Sat: 9:00 AM - 7:00 PM\nSun: 10:00 AM - 5:00 PM' }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form & Map -->
<section class="contact-form-section section-padding bg-light">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="contact-form-wrapper">
                    <h2 class="section-title">Send Us a Message</h2>
                    <p class="section-subtitle">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
                    
                    <form id="contactForm" class="contact-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" name="firstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="interest" class="form-label">I'm Interested In *</label>
                            <select class="form-control" id="interest" name="interest" required>
                                <option value="">Select Your Interest</option>
                                <option value="buying">Buying a Property</option>
                                <option value="selling">Selling a Property</option>
                                <option value="renting">Renting a Property</option>
                                <option value="investment">Investment Opportunities</option>
                                <option value="commercial">Commercial Properties</option>
                                <option value="consultation">Property Consultation</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="budget" class="form-label">Budget Range</label>
                            <select class="form-control" id="budget" name="budget">
                                <option value="">Select Budget Range</option>
                                <option value="under-50">Under ₹50 Lakhs</option>
                                <option value="50-100">₹50 Lakhs - ₹1 Crore</option>
                                <option value="100-200">₹1 Crore - ₹2 Crores</option>
                                <option value="200-500">₹2 Crores - ₹5 Crores</option>
                                <option value="above-500">Above ₹5 Crores</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="location" class="form-label">Preferred Location</label>
                            <input type="text" class="form-control" id="location" name="location" placeholder="e.g., Pune, Mumbai, Gurgaon">
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" placeholder="Tell us about your requirements..." required></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                            <label class="form-check-label" for="newsletter">
                                I would like to receive updates about new properties and market insights
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information & Map -->
            <div class="col-lg-6">
                <div class="contact-info-wrapper">
                    <h3 class="mb-4">Why Choose Hestia Abodes?</h3>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Expert Consultation</h5>
                            <p>Get personalized advice from our experienced real estate professionals.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Trusted Service</h5>
                            <p>98% client satisfaction rate with transparent and reliable service.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="feature-content">
                            <h5>Quick Response</h5>
                            <p>We respond to all inquiries within 24 hours, often much sooner.</p>
                        </div>
                    </div>
                    <div class="contact-feature">
                        <div class="feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="feature-content">
                            <h5>End-to-End Support</h5>
                            <p>From property search to final handover, we're with you every step.</p>
                        </div>
                    </div>
                    
                    <!-- Map Placeholder -->
                    <div class="map-container mt-4">
                        <div class="map-placeholder">
                            <i class="fas fa-map-marked-alt"></i>
                            <h5>Find Us in Pune</h5>
                            <p>We're located in the heart of Pune, Maharashtra. Contact us to schedule a visit to our office.</p>
                            <a href="https://maps.google.com" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-directions me-2"></i>Get Directions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Contact Options -->
<section class="quick-contact section-padding">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Need Immediate Assistance?</h2>
            <p class="section-subtitle">Choose your preferred way to connect with us</p>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="quick-contact-card">
                    <div class="quick-contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4>Call Now</h4>
                    <p>Speak directly with our property experts</p>
                    <a href="tel:{{ str_replace(['+', ' ', '-'], '', $settings['contact_phone'] ?? '+************') }}" class="btn btn-success">{{ $settings['contact_phone'] ?? '+91 ************' }}</a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="quick-contact-card">
                    <div class="quick-contact-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h4>WhatsApp</h4>
                    <p>Quick chat for instant property queries</p>
                    <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $settings['contact_whatsapp'] ?? $settings['contact_phone'] ?? '************') }}" target="_blank" class="btn btn-success">Chat on WhatsApp</a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="quick-contact-card">
                    <div class="quick-contact-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <h4>Schedule Meeting</h4>
                    <p>Book a consultation at your convenience</p>
                    <a href="#" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#scheduleModal">Book Appointment</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Schedule Meeting Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleModalLabel">Schedule a Meeting</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="mb-3">
                        <label for="meetingName" class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="meetingName" required>
                    </div>
                    <div class="mb-3">
                        <label for="meetingPhone" class="form-label">Phone Number *</label>
                        <input type="tel" class="form-control" id="meetingPhone" required>
                    </div>
                    <div class="mb-3">
                        <label for="meetingDate" class="form-label">Preferred Date *</label>
                        <input type="date" class="form-control" id="meetingDate" required>
                    </div>
                    <div class="mb-3">
                        <label for="meetingTime" class="form-label">Preferred Time *</label>
                        <select class="form-control" id="meetingTime" required>
                            <option value="">Select Time</option>
                            <option value="09:00">9:00 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="12:00">12:00 PM</option>
                            <option value="14:00">2:00 PM</option>
                            <option value="15:00">3:00 PM</option>
                            <option value="16:00">4:00 PM</option>
                            <option value="17:00">5:00 PM</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="meetingType" class="form-label">Meeting Type *</label>
                        <select class="form-control" id="meetingType" required>
                            <option value="">Select Meeting Type</option>
                            <option value="office">Office Visit</option>
                            <option value="site">Site Visit</option>
                            <option value="video">Video Call</option>
                            <option value="phone">Phone Call</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="meetingPurpose" class="form-label">Purpose</label>
                        <textarea class="form-control" id="meetingPurpose" rows="3" placeholder="Brief description of what you'd like to discuss"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="scheduleForm" class="btn btn-primary">Schedule Meeting</button>
            </div>
        </div>
    </div>
</div>
@endsection
