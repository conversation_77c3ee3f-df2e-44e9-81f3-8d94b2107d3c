/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Import Swiper CSS */
@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';

/* Import Montserrat Font */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');

/* Custom CSS for Hestia Abodes #2c5530 */
:root {
    --primary-color: #cfaa13;
    --secondary-color: #030303;
    --accent-color:#8b751d;
    --light-gold: #cfaa13;
    --platinum-grey: #8e8e93;
    --ivory: #f5f5dc;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --bg-light: #f8f9fa;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

a {
   font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    color: #ffffff !important;
   
    
}
/* Global Heading Styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #000000;
}

h1, h2 {
    font-weight: 700;
}

.card-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #000000;
}

/* Navbar Styles */
.navbar {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: none;
    transition: all 0.3s ease;
    z-index: 1030;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    pointer-events: auto;
    transform: translateY(0);
}

.navbar.navbar-hidden {
    transform: translateY(-100%);
}

/* Ensure smooth transitions and prevent layout shift */
.navbar {
    will-change: transform;
}

/* Prevent dropdown issues when navbar is hidden */
.navbar.navbar-hidden .dropdown-menu {
    display: none !important;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: none;
    transition: all 0.3s ease;
    z-index: 1030;
    padding: 15px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.navbar.scrolled .navbar-brand {
    background: #8b751d;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.navbar.scrolled .navbar-logo {
    filter: brightness(0) invert(1) drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}

/* Brand Styling */
.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.navbar-brand:hover {
    background: rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.navbar-logo {
    height: 80px;
    width: auto;
    filter: brightness(0) invert(1) drop-shadow(3px 3px 8px rgba(0, 0, 0, 0.9));
    transition: all 0.3s ease;
    max-width: 250px;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    color: white !important;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
    padding: 8px 16px !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative;
    z-index: 1031;
}

.navbar-nav .nav-link:hover {
    color: var(--light-gold) !important;
    transform: translateY(-1px);
    text-shadow: 0 0 10px var(--light-gold);
}

.dropdown-menu {
    border: none;
    box-shadow: none;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.dropdown-menu .dropdown-item {
    color: white;
}

.dropdown-menu .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Icon Button Styling */
.btn-icon {
    background: transparent;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin: 0 5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-icon:hover {
    background: transparent;
    border: none;
    color: var(--light-gold);
    transform: scale(1.1);
    text-shadow: 0 0 10px var(--light-gold);
}

.btn-icon i {
    font-size: 14px;
}

/* Phone icon in dropdown */
.navbar-nav .dropdown-toggle i {
    font-size: 14px;
}

/* Search Input */
.search-input {
    display: none;
    transition: all 0.3s ease;
}

.search-input.show {
    display: block;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 500px;
    height: 100vh;
    background: rgb(7, 7, 7);
    z-index: 1050;
    transition: right 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.sidebar.show {
    right: 0;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

/* Hero Slider */
.hero-slider {
    height: 100vh;
    min-height: 600px;
    margin-top: 0;
    position: relative;
}

.hero-slide {
    background-size: cover;
    background-position: center;
    position: relative;
}

.hero-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 2;
    padding: 0 20px;
    max-width: 800px;
}

.hero-content h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1.5rem;
    color: white;
}

.hero-content p {
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-size: 1.25rem;
    color: white;
}

.hero-content .btn {
    font-family: 'Montserrat', sans-serif;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid transparent;
    font-weight: 600;
    padding: 12px 30px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.hero-content .btn-primary {
    background: var(--light-gold);
    border-color: var(--light-gold);
    color: var(--text-dark);
}

.hero-content .btn-outline-light {
    border-color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.hero-content .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

/* Section Styles */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 1rem;
    text-shadow: none;
}

.section-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 400;
    color: var(--text-light);
    margin-bottom: 3rem;
}

/* Property Cards */
.property-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.property-card img {
    height: 250px;
    object-fit: cover;
}

/* Full Screen Featured Projects */
.featured-projects-fullscreen {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.featured-section-title {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    text-align: center;
}

.featured-section-title h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    letter-spacing: 2px;
}

.featured-project-slide {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
}

.featured-project-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.1) 100%);
    z-index: 1;
}

.featured-project-content {
    position: absolute;
    bottom: 40px;
    left: 40px;
    z-index: 2;
    color: white;
    max-width: 350px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
}

.featured-project-content:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-5px);
}

.featured-project-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.featured-project-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--light-gold);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.project-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.project-detail-item {
    font-family: 'Montserrat', sans-serif;
    font-size: 0.95rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.project-detail-label {
    color: rgba(255, 255, 255, 0.7);
}

.project-detail-value {
    font-weight: 600;
    color: white;
}

.project-price {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.4rem;
    font-weight: 800;
    color: var(--light-gold);
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.project-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.project-action-btn:hover {
    background: var(--light-gold);
    border-color: var(--light-gold);
    color: #000000;
    transform: translateY(-2px);
}

/* Featured Projects Navigation */
.featured-projects-nav {
    position: absolute;
    bottom: 30px;
    left: 50px;
    z-index: 3;
    display: flex;
    gap: 15px;
}

.featured-nav-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.featured-nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--light-gold);
    color: var(--light-gold);
    transform: scale(1.1);
}

/* Featured Projects Pagination */
.featured-projects-pagination {
    position: absolute;
    bottom: 30px;
    right: 50px;
    z-index: 3;
    display: flex;
    gap: 10px;
}

.featured-projects-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.featured-projects-pagination .swiper-pagination-bullet-active {
    background: var(--light-gold);
    transform: scale(1.3);
}

/* Service Cards */
.service-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #000000;
    margin-top: 1rem;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    background: var(--light-gold);
    color: var(--text-dark);
    box-shadow: 0 4px 15px rgba(201, 176, 55, 0.4);
}

/* Testimonial Styles */
.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 1rem;
}

/* Footer Styles */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 3rem 0 1rem;
}

.footer h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: white;
    margin-bottom: 1rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--light-gold);
}

.bottom-footer {
    background: #1a252f;
    padding: 1rem 0;
    border-top: 1px solid #495057;
}

.footer-logo {
    filter: brightness(0) invert(1);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--primary-color) !important;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white !important;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(207, 170, 19, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    background: var(--accent-color) !important;
    color: white !important;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(207, 170, 19, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slider {
        height: 100vh;
        min-height: 500px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-content .btn {
        padding: 10px 25px;
        font-size: 1rem;
        margin: 5px;
    }

    .section-title {
        font-size: 2rem;
    }

    .sidebar {
        width: 100%;
        right: -100%;
    }

    .navbar-logo {
        height: 60px;
    }

    .navbar-brand {
        padding: 8px 15px;
    }

    /* Featured Projects Mobile */
    .featured-project-content {
        bottom: 20px;
        left: 20px;
        max-width: 280px;
        padding: 20px;
    }

    .featured-project-title {
        font-size: 1.4rem;
    }

    .featured-project-subtitle {
        font-size: 0.8rem;
    }

    .project-detail-item {
        font-size: 0.85rem;
    }

    .project-price {
        font-size: 1.2rem;
    }

    .featured-projects-nav {
        left: 20px;
        bottom: 20px;
    }

    .featured-nav-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .featured-projects-pagination {
        right: 20px;
        bottom: 20px;
    }

    .project-action-btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    .featured-section-title {
        top: 30px;
    }

    .featured-section-title h2 {
        font-size: 1.8rem;
        letter-spacing: 1px;
    }
}

/* Projects Page Styles */
.page-header {
    background: url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    padding: 140px 0 100px;
    margin-top: 0;
    color: white;
    text-align: center;
    position: relative;
    min-height: 60vh;
}



.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header .row {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



.page-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.page-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.breadcrumb {
    background: rgba(231, 209, 8, 0.1);
    margin-bottom: 0;
    padding: 12px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: inline-flex;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
    font-weight: 600;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.7);
}

.section-padding-sm {
    padding: 40px 0;
}

/* Project Filters */
.filter-tabs {
    margin-bottom: 2rem;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 10px 25px;
    margin: 5px;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(207, 170, 19, 0.3);
}

/* Project Cards */
.project-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-status {
    background: var(--light-gold);
    color: var(--text-dark);
    padding: 5px 15px;
    border-radius: 20px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    align-self: flex-start;
}

.project-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.project-actions .btn {
    font-size: 0.8rem;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
}

.project-content {
    padding: 25px;
}

.project-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.project-name {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.project-location {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.project-location i {
    color: var(--primary-color);
    margin-right: 5px;
}

.project-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.detail-label {
    color: var(--text-light);
    font-weight: 500;
}

.detail-value {
    color: var(--text-dark);
    font-weight: 600;
}

.detail-value.price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1rem;
}

.project-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.feature-tag {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(207, 170, 19, 0.2);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--light-gold) 100%);
}

.cta-section .btn-light {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
}

.cta-section .btn-light:hover {
    background: transparent;
    color: white;
    border-color: white;
}



/* Contact Page Styles */
.contact-info {
    background: white;
}

.contact-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(207, 170, 19, 0.1);
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

.contact-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.contact-card p {
    color: var(--text-light);
    margin-bottom: 0;
    line-height: 1.6;
}

.contact-card a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.contact-card a:hover {
    color: var(--light-gold);
}

/* Contact Form Section */
.contact-form-section {
    background: #f8f9fa;
}

.contact-form-wrapper {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form .form-label {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-family: 'Montserrat', sans-serif;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(207, 170, 19, 0.25);
}

.contact-form .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border: none;
    padding: 15px 30px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

/* Contact Info Wrapper */
.contact-info-wrapper {
    padding: 2rem;
}

.contact-info-wrapper h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.contact-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.contact-feature:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.feature-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: var(--text-light);
    margin-bottom: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Map Container */
.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.map-placeholder {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 3rem 2rem;
    text-align: center;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.map-placeholder h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1rem;
}

.map-placeholder p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.map-placeholder .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    font-weight: 600;
}

.map-placeholder .btn:hover {
    background: white;
    color: var(--primary-color);
}

/* Quick Contact Section */
.quick-contact {
    background: white;
}

.quick-contact-card {
    text-align: center;
    padding: 2.5rem 2rem;
    border-radius: 20px;
    background: white;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.quick-contact-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.quick-contact-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2.5rem;
    transition: all 0.3s ease;
}

.quick-contact-card:hover .quick-contact-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(207, 170, 19, 0.4);
}

.quick-contact-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.quick-contact-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.quick-contact-card .btn {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.quick-contact-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Styling */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.modal-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
}

.btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1.5rem 2rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .contact-form-wrapper {
        padding: 2rem 1.5rem;
    }

    .contact-info-wrapper {
        padding: 1rem;
        margin-top: 2rem;
    }

    .contact-feature {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-right: 1rem;
    }

    .quick-contact-card {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .quick-contact-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .page-header {
        padding: 120px 0 80px;
        min-height: 50vh;
        background-attachment: scroll;
    }

    .page-title {
        font-size: 2.5rem;
        letter-spacing: 1px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .breadcrumb {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* Why Us Page Styles */
.who-we-are {
    background: white;
}

.about-image-wrapper {
    position: relative;
}

.about-image-wrapper img {
    width: 100%;
    height: auto;
    border-radius: 15px;
}

.experience-badge {
    position: absolute;
    bottom: -20px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.experience-badge h3 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    line-height: 1;
}

.experience-badge p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.about-content {
    padding-left: 2rem;
}

.about-content .lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.about-content p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.2rem;
}

.core-values {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.value-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.value-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* Our Expertise Section */
.our-expertise {
    background: #f8f9fa;
}

.expertise-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    height: 100%;
    transition: all 0.3s ease;
}

.expertise-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.expertise-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.expertise-card h4 i {
    color: var(--primary-color);
}

.expertise-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.expertise-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem 0;
    color: var(--text-light);
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
}

.expertise-list li:last-child {
    border-bottom: none;
}

.expertise-list li i {
    color: var(--primary-color);
    font-size: 1rem;
}

.expertise-summary {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;
}

.expertise-summary .lead {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0;
}

/* Mission & Vision Section */
.mission-vision {
    background: white;
}

.mission-card,
.vision-card {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    padding: 3rem 2.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    height: 100%;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.mission-card:hover,
.vision-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 2rem;
}

.mission-card h3,
.vision-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.mission-card p,
.vision-card p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1rem;
}

.vision-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.vision-list li {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.vision-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* What Sets Us Apart Section */
.what-sets-apart {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--light-gold) 100%);
}

.apart-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.apart-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.apart-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.apart-card:hover .apart-icon {
    background: white;
    color: var(--primary-color);
    transform: scale(1.1);
}

.apart-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.apart-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
}

.apart-summary {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.apart-summary h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
}

/* Meet the Founder Section */
.meet-founder {
    background: white;
}

.founder-image-wrapper {
    position: relative;
    text-align: center;
}

.founder-image-wrapper img {
    width: 100%;
    max-width: 300px;
    height: auto;
    border-radius: 20px;
}

.founder-badge {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    min-width: 200px;
}

.founder-badge h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin: 0;
    font-size: 1.1rem;
}

.founder-badge p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.founder-content {
    padding-left: 2rem;
}

.founder-content .lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.founder-content p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.founder-principles {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
}

.principle-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-weight: 500;
    color: var(--text-dark);
}

.principle-item:last-child {
    margin-bottom: 0;
}

.principle-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.founder-quote {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    margin: 2rem 0;
    position: relative;
    border-left: none;
}

.founder-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
    font-family: serif;
}

.founder-quote p {
    font-size: 1.2rem;
    font-style: italic;
    margin: 0;
    color: white;
    line-height: 1.6;
}

.founder-quote cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 600;
    font-style: normal;
    color: rgba(255, 255, 255, 0.9);
}

/* Mobile Responsive for Why Us */
@media (max-width: 768px) {
    .about-content {
        padding-left: 0;
        margin-top: 2rem;
    }

    .core-values {
        flex-direction: column;
        gap: 1rem;
    }

    .founder-content {
        padding-left: 0;
        margin-top: 2rem;
    }

    .founder-principles {
        padding: 1.5rem;
    }

    .founder-quote {
        padding: 2rem 1.5rem;
    }

    .founder-quote p {
        font-size: 1.1rem;
    }

    .experience-badge {
        bottom: -15px;
        right: 15px;
        padding: 15px;
    }

    .experience-badge h3 {
        font-size: 2rem;
    }
}

/* Blog Page Styles */
.page-header.blog-header {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('../images/hero-bg.jpg');
}

/* Featured Article */
.featured-article {
    background: white;
}

.featured-post {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border-radius: 25px;
    padding: 3rem;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(207, 170, 19, 0.1);
    transition: all 0.3s ease;
}

.featured-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.featured-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-post:hover .featured-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.featured-content {
    padding-left: 2rem;
}

.featured-content h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.featured-content .lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.featured-content p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

/* Post Meta */
.post-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.post-meta span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.post-meta .category {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: 600;
}

.post-meta .date::before {
    content: '📅';
    margin-right: 5px;
}

.post-meta .read-time::before {
    content: '⏱️';
    margin-right: 5px;
}

/* Blog Categories */
.blog-categories {
    background: #f8f9fa;
}

.category-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.category-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: var(--text-dark);
    padding: 10px 25px;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(207, 170, 19, 0.3);
}

/* Blog Articles */
.blog-articles {
    background: white;
}

.blog-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.blog-image {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.1);
}

.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.blog-content {
    padding: 2rem;
}

.blog-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.blog-meta span {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

.blog-meta .author {
    color: var(--primary-color);
    font-weight: 600;
}

.blog-content h3 {
    margin-bottom: 1rem;
}

.blog-content h3 a {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 1.3rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.blog-content h3 a:hover {
    color: var(--primary-color);
}

.blog-content p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* Blog Tags */
.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 1.5rem;
}

.tag {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(207, 170, 19, 0.2);
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Read More Link */
.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.read-more:hover {
    color: var(--light-gold);
    transform: translateX(5px);
}

.read-more i {
    transition: transform 0.3s ease;
}

.read-more:hover i {
    transform: translateX(3px);
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--light-gold) 100%);
}

.newsletter-form .input-group {
    max-width: 500px;
    margin: 0 auto;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.newsletter-form .form-control {
    border: none;
    padding: 15px 25px;
    font-size: 1rem;
    background: white;
}

.newsletter-form .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.newsletter-form .btn {
    border: none;
    padding: 15px 30px;
    font-weight: 600;
    color: var(--primary-color);
    background: white;
    transition: all 0.3s ease;
}

.newsletter-form .btn:hover {
    background: #f8f9fa;
    transform: translateX(-2px);
}

/* Load More Button */
#loadMoreBtn {
    margin-top: 2rem;
    padding: 15px 40px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

#loadMoreBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.3);
}

/* Mobile Responsive for Blog */
@media (max-width: 768px) {
    .featured-post {
        padding: 2rem 1.5rem;
    }

    .featured-content {
        padding-left: 0;
        margin-top: 2rem;
    }

    .featured-content h2 {
        font-size: 1.8rem;
    }

    .category-filters {
        justify-content: center;
    }

    .category-btn {
        padding: 8px 20px;
        font-size: 0.8rem;
    }

    .blog-content {
        padding: 1.5rem;
    }

    .blog-content h3 a {
        font-size: 1.2rem;
    }

    .post-meta,
    .blog-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .newsletter-form .input-group {
        flex-direction: column;
        border-radius: 15px;
    }

    .newsletter-form .form-control,
    .newsletter-form .btn {
        border-radius: 15px;
        margin-bottom: 10px;
    }

    .newsletter-form .btn {
        margin-bottom: 0;
    }
}

/* Blog Item Animation */
.blog-item {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.blog-item.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* SEO Optimized Content Styling */
.blog-content h3 a {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-content p {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Media Center Page Styles */
.page-header.media-header {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('../images/about-image.jpg');
}

/* Media Categories */
.media-categories {
    background: #f8f9fa;
}

.media-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.media-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: var(--text-dark);
    padding: 10px 25px;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.media-btn:hover,
.media-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(207, 170, 19, 0.3);
}

/* Featured Media */
.featured-media {
    background: white;
}

.featured-media-card {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(207, 170, 19, 0.1);
    transition: all 0.3s ease;
}

.featured-media-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.media-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.media-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-media-card:hover .media-image img {
    transform: scale(1.05);
}

.media-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.play-button:hover {
    background: var(--primary-color);
    color: white;
    transform: translate(-50%, -50%) scale(1.1);
}

.media-content {
    padding: 2.5rem;
}

.media-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.media-meta span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.media-meta .category {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: 600;
}

.media-meta .source {
    color: var(--primary-color);
    font-weight: 600;
}

.media-content h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    font-size: 1.8rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.media-content p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

/* Trending Media */
.trending-media {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.trending-media h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.trending-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.trending-item:last-child {
    border-bottom: none;
}

.trending-item:hover {
    background: #f8f9fa;
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: 10px;
}

.trending-image {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.trending-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trending-content {
    flex: 1;
}

.trending-category {
    font-size: 0.8rem;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.trending-content h6 {
    margin: 0.5rem 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

.trending-content h6 a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.trending-content h6 a:hover {
    color: var(--primary-color);
}

.trending-date {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Media Grid */
.media-grid {
    background: #f8f9fa;
}

.media-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.media-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.media-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.media-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-card:hover .media-card-image img {
    transform: scale(1.1);
}

.media-type {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.video-duration {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.video-play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.video-play-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translate(-50%, -50%) scale(1.1);
}

.media-card-content {
    padding: 2rem;
}

.media-card-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.media-card-meta span {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

.media-card-meta .category {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: 600;
}

.media-card-meta .source {
    color: var(--primary-color);
    font-weight: 600;
}

.media-card h4 {
    margin-bottom: 1rem;
}

.media-card h4 a {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 1.2rem;
    line-height: 1.4;
    transition: color 0.3s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.media-card h4 a:hover {
    color: var(--primary-color);
}

.media-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.media-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.media-actions .btn {
    font-size: 0.9rem;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 600;
}

.media-actions .btn-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 0;
}

.media-actions .btn-link:hover {
    color: var(--light-gold);
    text-decoration: underline;
}

/* Media Kit Section */
.media-kit {
    background: white;
}

.media-kit-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.media-kit-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem 0;
    color: var(--text-light);
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
}

.media-kit-list li:last-child {
    border-bottom: none;
}

.media-kit-list li i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.media-kit-actions {
    margin-top: 2rem;
}

.media-kit-preview {
    position: relative;
    text-align: center;
}

.media-kit-preview img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 20px;
}

.download-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 15px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.download-badge:hover {
    transform: scale(1.05);
}

.download-badge i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.download-badge span {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Media Item Animation */
.media-item {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.media-item.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* Load More Media Button */
#loadMoreMedia {
    margin-top: 2rem;
    padding: 15px 40px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

#loadMoreMedia:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.3);
}

/* Mobile Responsive for Media Center */
@media (max-width: 768px) {
    .media-filters {
        justify-content: center;
    }

    .media-btn {
        padding: 8px 20px;
        font-size: 0.8rem;
    }

    .featured-media-card {
        margin-bottom: 2rem;
    }

    .media-content {
        padding: 2rem 1.5rem;
    }

    .media-content h3 {
        font-size: 1.5rem;
    }

    .media-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .trending-media {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .trending-item {
        flex-direction: column;
        gap: 0.5rem;
    }

    .trending-image {
        width: 100%;
        height: 150px;
    }

    .media-card-content {
        padding: 1.5rem;
    }

    .media-card h4 a {
        font-size: 1.1rem;
    }

    .media-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .media-actions .btn {
        width: 100%;
        text-align: center;
    }

    .media-kit-actions {
        text-align: center;
    }

    .media-kit-actions .btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    .media-kit-preview {
        margin-top: 2rem;
    }

    .download-badge {
        bottom: 15px;
        right: 15px;
        padding: 12px 15px;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .video-play-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Project Details Page Styles */
.project-hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.project-hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.project-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.project-slide.active {
    opacity: 1;
}

.project-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%);
}

.project-hero-content {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    z-index: 2;
    color: white;
}

.project-badges {
    margin-bottom: 1.5rem;
}

.badge-premium,
.badge-status {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.badge-premium {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
}

.badge-status {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.project-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
}

.project-location {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.project-location i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.project-highlights {
    display: flex;
    gap: 3rem;
    flex-wrap: wrap;
}

.highlight-item {
    text-align: center;
}

.highlight-label {
    display: block;
    font-size: 0.9rem;
    color:#cfaa13;
    opacity: 0.8;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.highlight-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.project-cta-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
}

.project-cta-card h4 {
    color: white;
    margin-bottom: 1rem;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
}

.project-cta-card p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.contact-info p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.contact-info i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* Hero Navigation */
.hero-navigation {
    position: absolute;
    top: 50%;
    width: 100%;
    z-index: 3;
    pointer-events: none;
}

.hero-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
}

.hero-nav-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.hero-nav-btn.prev {
    left: 30px;
}

.hero-nav-btn.next {
    right: 30px;
}

/* Hero Indicators */
.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Project Navigation */
.project-nav-section {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 80px;
    z-index: 100;
}

.project-nav {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 1rem 0;
    flex-wrap: wrap;
}

.nav-item {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

/* .nav-item:hover,
.nav-item.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
} */

/* Project Sections */
.project-section {
    scroll-margin-top: 150px;
}

.project-content .lead {
    font-size: 1.2rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.project-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(207, 170, 19, 0.1);
    transform: translateX(10px);
}

.feature-item i {
    color: white;
    font-size: 2rem;
    margin-top: 0.5rem;
}

.feature-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.6;
}

/* Project Sidebar */
.project-sidebar {
    padding-left: 2rem;
}

.project-specs,
.developer-info {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: 2px solid rgba(207, 170, 19, 0.1);
}

.project-specs h4,
.developer-info h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    color: var(--text-light);
    font-weight: 500;
}

.spec-value {
    color: var(--text-dark);
    font-weight: 600;
}

.developer-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
}

.developer-logo {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
}

.developer-details h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.developer-details p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Floor Plans */
.floor-plan-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.plan-tab {
    background: white;
    border: 2px solid #e9ecef;
    color: var(--text-dark);
    padding: 12px 30px;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.plan-tab:hover,
.plan-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(207, 170, 19, 0.3);
}

.floor-plan-content {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.plan-details {
    display: none;
}

.plan-details.active {
    display: block;
}

.plan-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.plan-info {
    padding-left: 2rem;
}

.plan-info h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.plan-specs {
    margin-bottom: 2rem;
}

.spec-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-row .spec-label {
    color: var(--text-light);
    font-weight: 500;
    font-size: 1rem;
}

.spec-row .spec-value {
    color: var(--text-dark);
    font-weight: 600;
    font-size: 1rem;
}

.plan-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.plan-actions .btn {
    padding: 12px 25px;
    font-weight: 600;
    border-radius: 25px;
}

/* Amenities */
.amenities-grid {
    margin-top: 2rem;
}

.amenity-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.amenity-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.amenity-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.amenity-card:hover .amenity-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

.amenity-card h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.amenity-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Project Contact */
.project-contact {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--light-gold) 100%);
}

.contact-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-actions .btn {
    padding: 15px 30px;
    font-weight: 600;
    border-radius: 25px;
    font-size: 1.1rem;
}

/* Blog Details Page Styles */
.blog-hero {
    position: relative;
    height: 70vh;
    min-height: 500px;
    overflow: hidden;
}

.blog-hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.blog-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.5) 100%);
}

.blog-hero-content {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    z-index: 2;
    color: white;
}

.blog-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.blog-meta span {
    font-size: 0.9rem;
    font-weight: 500;
}

.blog-category {
    background: var(--primary-color);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.blog-date::before {
    content: '📅';
    margin-right: 5px;
}

.blog-read-time::before {
    content: '⏱️';
    margin-right: 5px;
}

.blog-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
}

.blog-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.blog-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.author-info {
    text-align: left;
}

.author-name {
    display: block;
    font-weight: 600;
    font-size: 1.1rem;
}

.author-title {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Blog Content */
.blog-content-section {
    background: white;
}

.blog-article {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.article-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
}

.article-content .lead {
    font-size: 1.3rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.article-content h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin: 2.5rem 0 1.5rem;
    font-size: 2rem;
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.article-quote {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    margin: 2.5rem 0;
    position: relative;
    border-left: none;
}

.article-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
    font-family: serif;
}

.article-quote p {
    font-size: 1.3rem;
    font-style: italic;
    margin: 0;
    line-height: 1.6;
    text-align: center;
}

.article-quote cite {
    display: block;
    text-align: right;
    margin-top: 1rem;
    font-weight: 600;
    font-style: normal;
    opacity: 0.9;
}

.article-image {
    margin: 2.5rem 0;
    text-align: center;
}

.article-image img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.image-caption {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
    margin-top: 1rem;
    margin-bottom: 0;
}

.article-list {
    margin: 2rem 0;
    padding-left: 0;
    list-style: none;
}

.article-list li {
    margin-bottom: 1rem;
    padding-left: 2rem;
    position: relative;
    line-height: 1.7;
}

.article-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2.5rem 0;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 20px;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-item h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-light);
    font-weight: 500;
    margin: 0;
    font-size: 0.9rem;
}

.article-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    margin: 2.5rem 0;
}

.article-cta h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.article-cta p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.article-cta .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
    padding: 12px 30px;
}

.article-cta .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* Article Tags & Social Share */
.article-tags,
.article-share {
    margin: 2rem 0;
    padding: 1.5rem 0;
    border-top: 1px solid #e9ecef;
}

.article-tags h5,
.article-share h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.article-tags .tag {
    display: inline-block;
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(207, 170, 19, 0.2);
    transition: all 0.3s ease;
}

.article-tags .tag:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.share-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 8px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.share-btn.twitter {
    background: #1da1f2;
    color: white;
}

.share-btn.linkedin {
    background: #0077b5;
    color: white;
}

.share-btn.whatsapp {
    background: #25d366;
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    color: white;
}

/* Author Bio */
.author-bio {
    background: #f8f9fa;
    padding: 2.5rem;
    border-radius: 20px;
    margin: 2rem 0;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.author-avatar-large {
    flex-shrink: 0;
}

.author-avatar-large img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
}

.author-details h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.author-designation {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.author-details p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.author-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--light-gold);
    color: white;
    transform: translateY(-2px);
}

/* Blog Sidebar */
.blog-sidebar {
    padding-left: 2rem;
}

.sidebar-widget {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: 2px solid rgba(207, 170, 19, 0.1);
}

.widget-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.related-articles {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.related-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.related-item:hover {
    background: rgba(207, 170, 19, 0.1);
    transform: translateX(5px);
}

.related-image {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.related-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-content h6 {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    line-height: 1.4;
}

.related-content h6 a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.related-content h6 a:hover {
    color: var(--primary-color);
}

.related-date {
    font-size: 0.8rem;
    color: var(--text-light);
}

.newsletter-widget p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.newsletter-widget .input-group {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.newsletter-widget .form-control {
    border: none;
    padding: 12px 20px;
}

.newsletter-widget .btn {
    border: none;
    padding: 12px 20px;
    font-weight: 600;
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-item {
    background: rgba(207, 170, 19, 0.1);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid rgba(207, 170, 19, 0.2);
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.contact-widget {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.contact-widget h4 {
    color: white;
    margin-bottom: 1rem;
}

.contact-widget p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

.contact-widget .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
}

.contact-widget .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.contact-widget .contact-info {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 1rem;
}

.contact-widget .contact-info p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.contact-widget .contact-info i {
    margin-right: 0.5rem;
}

/* Comments Section */
.comments-section {
    background: #f8f9fa;
}

.comments-wrapper {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.comments-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.comment-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.comment-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.comment-avatar {
    flex-shrink: 0;
}

.comment-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.comment-author {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    font-size: 1.1rem;
}

.comment-date {
    color: var(--text-light);
    font-size: 0.9rem;
}

.comment-content p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.comment-reply {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.comment-reply:hover {
    color: var(--light-gold);
}

.comment-form {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.comment-form h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.comment-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.comment-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(207, 170, 19, 0.25);
}

.comment-form .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
}

/* Related Articles Section */
.related-articles-section {
    background: white;
}

.article-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.article-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.article-card .article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    margin: 0;
}

.article-card .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 0;
    box-shadow: none;
}

.article-card:hover .article-image img {
    transform: scale(1.1);
}

.article-card .article-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.article-card .article-content {
    padding: 2rem;
}

.article-card h4 {
    margin-bottom: 1rem;
}

.article-card h4 a {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    text-decoration: none;
    font-size: 1.2rem;
    line-height: 1.4;
    transition: color 0.3s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-card h4 a:hover {
    color: var(--primary-color);
}

.article-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-card .article-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-light);
}

.article-card .article-meta .author {
    color: var(--primary-color);
    font-weight: 600;
}

/* Mobile Responsive for Project & Blog Details */
@media (max-width: 768px) {
    .project-title {
        font-size: 2.5rem;
    }

    .project-highlights {
        flex-direction: column;
        gap: 1rem;
    }

    .project-cta-card {
        margin-top: 2rem;
    }

    .project-sidebar {
        padding-left: 0;
        margin-top: 2rem;
    }

    .floor-plan-tabs {
        flex-direction: column;
        align-items: center;
    }

    .plan-info {
        padding-left: 0;
        margin-top: 2rem;
    }

    .plan-actions {
        flex-direction: column;
    }

    .contact-actions {
        flex-direction: column;
    }

    .blog-title {
        font-size: 2.5rem;
    }

    .blog-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .blog-author {
        flex-direction: column;
        gap: 0.5rem;
    }

    .blog-article {
        padding: 2rem 1.5rem;
    }

    .article-content h2 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1.5rem;
    }

    .author-bio {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .blog-sidebar {
        padding-left: 0;
        margin-top: 2rem;
    }

    .sidebar-widget {
        padding: 1.5rem;
    }

    .related-item {
        flex-direction: column;
    }

    .related-image {
        width: 100%;
        height: 150px;
    }

    .comments-wrapper {
        padding: 2rem 1.5rem;
    }

    .comment-item {
        flex-direction: column;
        gap: 1rem;
    }

    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Services Page Styles */
.page-header.services-header {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('../images/default-property.jpg');
}

/* Services Overview */
.services-overview {
    background: white;
}

.service-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    padding: 3rem 2.5rem;
    border-radius: 25px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid rgba(207, 170, 19, 0.1);
    position: relative;
    overflow: hidden;
}

.service-overview-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(207, 170, 19, 0.1) 0%, rgba(207, 170, 19, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-overview-card:hover .card-background {
    opacity: 1;
}

.service-overview-card .service-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 3rem;
    transition: all 0.3s ease;
}

.service-overview-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(207, 170, 19, 0.4);
}

.service-overview-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.service-overview-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.service-stats {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: 'Montserrat', sans-serif;
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
    margin-top: 0.5rem;
}

.service-overview-card .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.service-overview-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

/* Service Features Highlight */
.service-features-highlight {
    background: #f8f9fa;
}

.features-content .lead {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.highlight-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.highlight-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.highlight-content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.6;
}

/* Features Visual */
.features-visual {
    position: relative;
}

.video-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
}

.video-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.video-container:hover .play-overlay {
    opacity: 1;
}

.video-container:hover img {
    transform: scale(1.05);
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.play-overlay:hover .play-button {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.play-text {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.floating-stats {
    position: absolute;
    bottom: -30px;
    right: 20px;
    display: flex;
    gap: 1rem;
}

.floating-stat {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 100px;
}

.floating-stat h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    margin: 0;
    line-height: 1;
}

.floating-stat p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0.5rem 0 0;
    font-weight: 500;
}

/* Home Buyers Section */
.home-buyers-section {
    background: #f8f9fa;
}

.service-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.service-description {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 2.5rem;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-item:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

.feature-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.feature-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Service Visual */
.service-visual {
    position: relative;
}

.service-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.service-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-image:hover img {
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.service-badge h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    line-height: 1;
    color: white;
}

.service-badge p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 600;
}

/* Enhanced Testimonial Carousel */
.testimonial-carousel {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    min-height: 200px;
}

.testimonial-slide {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.testimonial-slide.active {
    display: block;
    opacity: 1;
}

.quote-icon {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.author-details strong {
    display: block;
    color: var(--text-dark);
    font-weight: 600;
}

.author-details span {
    display: block;
    color: var(--text-light);
    font-size: 0.9rem;
}

.testimonial-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.testimonial-indicators .indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(207, 170, 19, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.testimonial-indicators .indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.cta-card {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.cta-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.cta-card p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.cta-card .cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-card .cta-buttons .btn {
    flex: 1;
    min-width: 150px;
}

.cta-card .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
    padding: 12px 25px;
}

.cta-card .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* Builders Section */
.builders-section {
    background: white;
}

.builder-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(207, 170, 19, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(207, 170, 19, 0.2);
    transform: translateX(10px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.stat-content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.builder-benefits {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.benefit-item:hover {
    background: rgba(207, 170, 19, 0.1);
    border-color: var(--primary-color);
    transform: translateX(10px);
}

.benefit-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Montserrat', sans-serif;
    font-weight: 800;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.benefit-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.benefit-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

.builder-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
}

.builder-cta h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.builder-cta .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
    padding: 12px 25px;
}

.builder-cta .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* Services Page Styles */
.page-header.services-header {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('../images/default-property.jpg');
}

/* Services Overview */
.services-overview {
    background: white;
}

.service-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    padding: 3rem 2.5rem;
    border-radius: 25px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid rgba(207, 170, 19, 0.1);
}

.service-overview-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.service-overview-card .service-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 3rem;
    transition: all 0.3s ease;
}

.service-overview-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(207, 170, 19, 0.4);
}

.service-overview-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.service-overview-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.service-overview-card .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.service-overview-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

/* Home Buyers Section */
.home-buyers-section {
    background: #f8f9fa;
}

.service-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.service-description {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 2.5rem;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-item:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(207, 170, 19, 0.4);
}

.feature-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.feature-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Service Visual */
.service-visual {
    position: relative;
}

.service-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.service-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-image:hover img {
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.service-badge h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    line-height: 1;
    color: white;
}

.service-badge p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 600;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.quote-icon {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    color: var(--primary-color);
    font-weight: 600;
}

.cta-card {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.cta-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.cta-card p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.cta-card .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
    padding: 12px 25px;
}

.cta-card .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* Builders Section */
.builders-section {
    background: white;
}

.builder-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(207, 170, 19, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(207, 170, 19, 0.2);
    transform: translateX(10px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.stat-content p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.builder-benefits {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.benefit-item:hover {
    background: rgba(207, 170, 19, 0.1);
    border-color: var(--primary-color);
    transform: translateX(10px);
}

.benefit-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Montserrat', sans-serif;
    font-weight: 800;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.benefit-content h5 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.benefit-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

.builder-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
}

.builder-cta h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.builder-cta .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    font-weight: 600;
    padding: 12px 25px;
}

.builder-cta .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* Process Section */
.process-section {
    background: #f8f9fa;
}

.process-step {
    text-align: center;
    padding: 2.5rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    border: 2px solid transparent;
}

.process-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(207, 170, 19, 0.4);
}

.step-icon {
    width: 80px;
    height: 80px;
    background: rgba(207, 170, 19, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem auto 1.5rem;
    color: var(--primary-color);
    font-size: 2rem;
    transition: all 0.3s ease;
}

.process-step:hover .step-icon {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.process-step h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.process-step p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Why Choose Services */
.why-choose-services {
    background: white;
}

.why-choose-card {
    text-align: center;
    padding: 2.5rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid rgba(207, 170, 19, 0.1);
}

.why-choose-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.why-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--light-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.why-choose-card:hover .why-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(207, 170, 19, 0.4);
}

.why-choose-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.why-choose-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Services CTA */
.services-cta {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--light-gold) 100%);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-weight: 600;
    border-radius: 25px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Mobile Responsive for Services */
@media (max-width: 768px) {
    .service-overview-card {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .service-overview-card .service-icon {
        width: 80px;
        height: 80px;
        font-size: 2.5rem;
    }

    .service-overview-card h3 {
        font-size: 1.5rem;
    }

    .service-title {
        font-size: 2rem;
    }

    .service-subtitle {
        font-size: 1.3rem;
    }

    .service-description {
        font-size: 1rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .service-image {
        margin-bottom: 1.5rem;
    }

    .service-image img {
        height: 250px;
    }

    .service-badge {
        top: 15px;
        right: 15px;
        padding: 15px;
    }

    .service-badge h4 {
        font-size: 2rem;
    }

    .testimonial-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .cta-card {
        padding: 2rem 1.5rem;
    }

    .builder-stats {
        gap: 1rem;
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .benefit-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .builder-cta {
        padding: 2rem 1.5rem;
    }

    .process-step {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .step-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin: 1.5rem auto 1rem;
    }

    .process-step h4 {
        font-size: 1.2rem;
    }

    .why-choose-card {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .why-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .why-choose-card h4 {
        font-size: 1.2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}
