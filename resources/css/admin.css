/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Import Font Awesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Modern Admin Dashboard CSS */
:root {
    /* Primary Brand Colors */
    --admin-primary: #cfaa13;
    --admin-primary-dark: #b8951a;
    --admin-primary-light: #e6c547;
    --admin-primary-rgb: 207, 170, 19;

    /* Modern Color Palette */
    --admin-secondary: #6366f1;
    --admin-success: #10b981;
    --admin-warning: #f59e0b;
    --admin-danger: #ef4444;
    --admin-info: #3b82f6;

    /* Neutral Colors */
    --admin-white: #ffffff;
    --admin-gray-50: #f9fafb;
    --admin-gray-100: #f3f4f6;
    --admin-gray-200: #e5e7eb;
    --admin-gray-300: #d1d5db;
    --admin-gray-400: #9ca3af;
    --admin-gray-500: #6b7280;
    --admin-gray-600: #4b5563;
    --admin-gray-700: #374151;
    --admin-gray-800: #1f2937;
    --admin-gray-900: #111827;

    /* Modern Gradients */
    --admin-gold-gradient: linear-gradient(135deg, #cfaa13 0%, #b8951a 100%);
    --admin-gold-gradient-soft: linear-gradient(135deg, rgba(207, 170, 19, 0.1) 0%, rgba(184, 149, 26, 0.05) 100%);
    --admin-glass-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Shadows */
    --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --admin-shadow-gold: 0 10px 25px -5px rgba(207, 170, 19, 0.2), 0 8px 10px -6px rgba(207, 170, 19, 0.1);

    /* Typography */
    --admin-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --admin-font-display: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Spacing */
    --admin-space-1: 0.25rem;
    --admin-space-2: 0.5rem;
    --admin-space-3: 0.75rem;
    --admin-space-4: 1rem;
    --admin-space-5: 1.25rem;
    --admin-space-6: 1.5rem;
    --admin-space-8: 2rem;
    --admin-space-10: 2.5rem;
    --admin-space-12: 3rem;

    /* Border Radius */
    --admin-radius-sm: 0.375rem;
    --admin-radius: 0.5rem;
    --admin-radius-md: 0.75rem;
    --admin-radius-lg: 1rem;
    --admin-radius-xl: 1.5rem;
    --admin-radius-2xl: 2rem;

    /* Transitions */
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --admin-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --admin-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--admin-font-primary);
    background: var(--admin-gray-50);
    color: var(--admin-gray-900);
    line-height: 1.6;
    font-size: 0.875rem;
    overflow-x: hidden;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--admin-gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--admin-gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--admin-primary);
}

/* Modern Admin Layout */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
    background: var(--admin-gray-50);
}

/* Ultra Modern Sidebar */
.admin-sidebar {
    width: 320px;
    background: linear-gradient(180deg, #1a1d29 0%, #16213e 50%, #0f172a 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1000;
    transition: var(--admin-transition);
    box-shadow:
        var(--admin-shadow-xl),
        inset -1px 0 0 rgba(207, 170, 19, 0.1);
    border-right: 1px solid rgba(207, 170, 19, 0.2);
    scrollbar-width: thin;
    scrollbar-color: rgba(207, 170, 19, 0.3) transparent;
}

/* Custom scrollbar for sidebar */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(207, 170, 19, 0.3);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(207, 170, 19, 0.5);
}

.admin-sidebar.collapsed {
    width: 90px;
}

.admin-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(207, 170, 19, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="sidebar-dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="10" cy="10" r="0.5" fill="rgba(207,170,19,0.03)"/><circle cx="50" cy="50" r="0.5" fill="rgba(207,170,19,0.03)"/></pattern></defs><rect width="60" height="60" fill="url(%23sidebar-dots)"/></svg>');
    pointer-events: none;
    animation: subtleFloat 8s ease-in-out infinite;
}

@keyframes subtleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

.sidebar-header {
    padding: var(--admin-space-8) var(--admin-space-6);
    border-bottom: 1px solid rgba(207, 170, 19, 0.2);
    text-align: center;
    background: var(--admin-gold-gradient);
    position: relative;
    overflow: hidden;
    margin-bottom: var(--admin-space-4);
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    animation: shine 6s infinite;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.sidebar-logo {
    font-family: var(--admin-font-display);
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    text-decoration: none;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--admin-space-3);
    position: relative;
    z-index: 1;
    transition: var(--admin-transition);
}

.sidebar-logo:hover {
    transform: scale(1.05);
    color: white;
}

.sidebar-logo i {
    font-size: 1.75rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo-text {
    transition: var(--admin-transition);
    letter-spacing: 0.5px;
}

.admin-sidebar.collapsed .logo-text {
    opacity: 0;
    transform: scale(0);
}

.admin-sidebar.collapsed .sidebar-logo i {
    font-size: 2rem;
}

.sidebar-nav {
    padding: 0 var(--admin-space-4);
    flex: 1;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--admin-space-6);
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: var(--admin-space-4) var(--admin-space-4) var(--admin-space-2);
    margin-bottom: var(--admin-space-2);
    position: relative;
    transition: var(--admin-transition);
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--admin-space-4);
    right: var(--admin-space-4);
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(207, 170, 19, 0.3), transparent);
}

.admin-sidebar.collapsed .nav-section-title {
    opacity: 0;
    transform: scale(0);
}

.nav-item {
    margin-bottom: var(--admin-space-1);
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--admin-space-4);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--admin-transition);
    border: none;
    background: none;
    width: 100%;
    font-weight: 500;
    font-size: 0.875rem;
    position: relative;
    border-radius: var(--admin-radius-lg);
    margin-bottom: var(--admin-space-1);
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--admin-gold-gradient);
    transform: scaleY(0);
    transition: var(--admin-transition);
    border-radius: 0 2px 2px 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--admin-gold-gradient);
    opacity: 0;
    transition: var(--admin-transition);
    border-radius: var(--admin-radius-lg);
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(207, 170, 19, 0.2);
}

.nav-link:hover::before,
.nav-link.active::before {
    transform: scaleY(1);
}

.nav-link:hover::after,
.nav-link.active::after {
    opacity: 0.1;
}

.nav-link-icon {
    width: 24px;
    height: 24px;
    margin-right: var(--admin-space-4);
    text-align: center;
    font-size: 1.125rem;
    transition: var(--admin-transition);
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-link:hover .nav-link-icon,
.nav-link.active .nav-link-icon {
    color: var(--admin-primary-light);
    transform: scale(1.1);
}

.nav-text {
    transition: var(--admin-transition);
    position: relative;
    z-index: 1;
    font-weight: 500;
}

.nav-badge {
    background: var(--admin-danger);
    color: white;
    font-size: 0.625rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
    position: relative;
    z-index: 1;
    animation: pulse 2s infinite;
}

/* Collapsed Sidebar States */
.admin-sidebar.collapsed .nav-text {
    opacity: 0;
    transform: translateX(-10px);
}

.admin-sidebar.collapsed .nav-badge {
    opacity: 0;
    transform: scale(0);
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--admin-space-4);
}

.admin-sidebar.collapsed .nav-link-icon {
    margin-right: 0;
}

/* Tooltip for collapsed sidebar */
.admin-sidebar.collapsed .nav-link {
    position: relative;
}

.admin-sidebar.collapsed .nav-link:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--admin-gray-900);
    color: white;
    padding: var(--admin-space-2) var(--admin-space-3);
    border-radius: var(--admin-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: var(--admin-space-2);
    box-shadow: var(--admin-shadow-lg);
}

/* Ultra Modern Main Content */
.admin-main {
    flex: 1;
    margin-left: 320px;
    transition: var(--admin-transition);
    min-height: 100vh;
    max-height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    background:
        radial-gradient(circle at 20% 20%, rgba(207, 170, 19, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.01) 0%, transparent 50%),
        var(--admin-gray-50);
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: rgba(207, 170, 19, 0.3) transparent;
}

/* Custom scrollbar for main content */
.admin-main::-webkit-scrollbar {
    width: 8px;
}

.admin-main::-webkit-scrollbar-track {
    background: var(--admin-gray-100);
}

.admin-main::-webkit-scrollbar-thumb {
    background: rgba(207, 170, 19, 0.3);
    border-radius: 4px;
}

.admin-main::-webkit-scrollbar-thumb:hover {
    background: rgba(207, 170, 19, 0.5);
}

.admin-main.expanded {
    margin-left: 90px;
}

.admin-main::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="main-pattern" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(207,170,19,0.01)"/><circle cx="25" cy="25" r="0.5" fill="rgba(99,102,241,0.01)"/><circle cx="75" cy="75" r="0.5" fill="rgba(99,102,241,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23main-pattern)"/></svg>');
    pointer-events: none;
    z-index: 0;
}

/* Modern Top Navigation */
.admin-topbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: var(--admin-space-4) var(--admin-space-8);
    border-bottom: 1px solid var(--admin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--admin-shadow-sm);
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: var(--admin-space-4);
}

.sidebar-toggle {
    background: var(--admin-gray-100);
    border: none;
    font-size: 1.125rem;
    color: var(--admin-gray-600);
    cursor: pointer;
    padding: var(--admin-space-3);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle:hover {
    background: var(--admin-primary);
    color: white;
    transform: scale(1.05);
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: var(--admin-space-4);
}

.topbar-user {
    display: flex;
    align-items: center;
    gap: var(--admin-space-3);
    padding: var(--admin-space-2) var(--admin-space-4);
    background: var(--admin-gray-100);
    border-radius: var(--admin-radius-lg);
    transition: var(--admin-transition);
    cursor: pointer;
}

.topbar-user:hover {
    background: var(--admin-gray-200);
    transform: translateY(-1px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--admin-gold-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    color: var(--admin-gray-900);
    font-size: 0.875rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--admin-gray-500);
    line-height: 1.2;
}

/* Enhanced Content Area */
.admin-content {
    padding: var(--admin-space-8);
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 80px);
}

.page-header {
    margin-bottom: var(--admin-space-8);
    position: relative;
    padding: var(--admin-space-6) 0;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100px;
    height: 3px;
    background: var(--admin-gold-gradient);
    border-radius: 2px;
}

.page-title {
    font-family: var(--admin-font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--admin-gray-900);
    margin-bottom: var(--admin-space-2);
    letter-spacing: -0.025em;
    position: relative;
    background: linear-gradient(135deg, var(--admin-gray-900) 0%, var(--admin-gray-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: var(--admin-gray-600);
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 1.6;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--admin-space-4);
    margin-top: var(--admin-space-4);
}

/* Breadcrumb */
.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: var(--admin-space-2);
    margin-bottom: var(--admin-space-4);
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: var(--admin-gray-500);
    text-decoration: none;
    transition: var(--admin-transition-fast);
}

.breadcrumb-item:hover {
    color: var(--admin-primary);
}

.breadcrumb-item.active {
    color: var(--admin-gray-900);
    font-weight: 600;
}

.breadcrumb-separator {
    color: var(--admin-gray-400);
}

/* Ultra Modern Cards */
.admin-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--admin-radius-xl);
    box-shadow:
        var(--admin-shadow-lg),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: var(--admin-transition);
    position: relative;
}

.admin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--admin-gold-gradient);
    opacity: 0;
    transition: var(--admin-transition);
}

.admin-card:hover {
    transform: translateY(-4px);
    box-shadow:
        var(--admin-shadow-xl),
        var(--admin-shadow-gold);
}

.admin-card:hover::before {
    opacity: 1;
}

.card-header {
    padding: var(--admin-space-6);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
    position: relative;
}

.card-header h5 {
    font-family: var(--admin-font-display);
    font-weight: 700;
    color: var(--admin-gray-900);
    margin: 0;
    font-size: 1.125rem;
}

.card-body {
    padding: var(--admin-space-6);
    position: relative;
}

/* Ultra Modern Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--admin-space-6);
    margin-bottom: var(--admin-space-8);
}

.stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: var(--admin-space-6);
    border-radius: var(--admin-radius-xl);
    box-shadow: var(--admin-shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: var(--admin-space-5);
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--admin-gold-gradient-soft);
    opacity: 0;
    transition: var(--admin-transition);
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--admin-shadow-xl), var(--admin-shadow-gold);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--admin-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 1;
    box-shadow: var(--admin-shadow-md);
    transition: var(--admin-transition);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-icon.primary {
    background: var(--admin-gold-gradient);
    box-shadow: var(--admin-shadow-gold);
}
.stat-icon.success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #059669 100%);
}
.stat-icon.warning {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #d97706 100%);
}
.stat-icon.info {
    background: linear-gradient(135deg, var(--admin-info) 0%, #2563eb 100%);
}

.stat-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.stat-content h3 {
    font-family: var(--admin-font-display);
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--admin-space-1);
    color: var(--admin-gray-900);
    line-height: 1;
    transition: var(--admin-transition);
}

.stat-card:hover .stat-content h3 {
    color: var(--admin-primary);
}

.stat-content p {
    color: var(--admin-gray-600);
    margin: 0;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--admin-space-1);
    margin-top: var(--admin-space-2);
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-trend.up {
    color: var(--admin-success);
}

.stat-trend.down {
    color: var(--admin-danger);
}

.stat-trend i {
    font-size: 0.625rem;
}

/* Tables */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--admin-border);
}

.admin-table th {
    background: #f8fafc;
    font-weight: 600;
    color: var(--admin-dark);
}

.admin-table tbody tr:hover {
    background: #f8fafc;
}

/* Modern Buttons */
.btn-admin {
    padding: var(--admin-space-3) var(--admin-space-6);
    border-radius: var(--admin-radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--admin-space-2);
    border: 2px solid transparent;
    cursor: pointer;
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;
    min-height: 44px;
}

.btn-admin::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--admin-transition);
}

.btn-admin:hover::before {
    left: 100%;
}

.btn-admin-primary {
    background: var(--admin-gold-gradient);
    color: white;
    box-shadow: var(--admin-shadow-md);
}

.btn-admin-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg), var(--admin-shadow-gold);
    color: white;
}

.btn-admin-primary:active {
    transform: translateY(0);
}

.btn-admin-success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #059669 100%);
    color: white;
    box-shadow: var(--admin-shadow-md);
}

.btn-admin-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
    color: white;
}

.btn-admin-warning {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #d97706 100%);
    color: white;
    box-shadow: var(--admin-shadow-md);
}

.btn-admin-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
    color: white;
}

.btn-admin-danger {
    background: linear-gradient(135deg, var(--admin-danger) 0%, #dc2626 100%);
    color: white;
    box-shadow: var(--admin-shadow-md);
}

.btn-admin-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
    color: white;
}

.btn-admin-outline {
    background: var(--admin-white);
    border-color: var(--admin-gray-300);
    color: var(--admin-gray-700);
    box-shadow: var(--admin-shadow-sm);
}

.btn-admin-outline:hover {
    background: var(--admin-gray-50);
    border-color: var(--admin-primary);
    color: var(--admin-primary);
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-md);
}

/* Button Sizes */
.btn-admin-sm {
    padding: var(--admin-space-2) var(--admin-space-4);
    font-size: 0.75rem;
    min-height: 36px;
}

.btn-admin-lg {
    padding: var(--admin-space-4) var(--admin-space-8);
    font-size: 1rem;
    min-height: 52px;
}

.btn-admin-xl {
    padding: var(--admin-space-5) var(--admin-space-10);
    font-size: 1.125rem;
    min-height: 60px;
}

/* Modern Checkbox */
.modern-checkbox {
    display: flex;
    align-items: center;
    margin: var(--admin-space-4) 0;
}

.checkbox-input {
    display: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-weight: 500;
    color: var(--admin-gray-700);
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--admin-gray-300);
    border-radius: var(--admin-radius-sm);
    margin-right: var(--admin-space-3);
    position: relative;
    transition: var(--admin-transition);
    background: var(--admin-white);
}

.checkbox-custom::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg) scale(0);
    transition: var(--admin-transition);
}

.checkbox-input:checked + .checkbox-label .checkbox-custom {
    background: var(--admin-gold-gradient);
    border-color: var(--admin-primary);
}

.checkbox-input:checked + .checkbox-label .checkbox-custom::after {
    transform: rotate(45deg) scale(1);
}

.checkbox-label:hover .checkbox-custom {
    border-color: var(--admin-primary);
}

/* Login Button Loading State */
.login-btn {
    position: relative;
    overflow: hidden;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Login Form Animation */
.login-form {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Form Validation */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--admin-space-2);
    font-size: 0.875rem;
    color: var(--admin-danger);
    font-weight: 500;
}

.form-control.is-invalid {
    border-color: var(--admin-danger);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-control.is-invalid:focus {
    border-color: var(--admin-danger);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
}

/* Modern Tooltip */
.modern-tooltip {
    position: absolute;
    background: var(--admin-gray-900);
    color: white;
    padding: var(--admin-space-2) var(--admin-space-3);
    border-radius: var(--admin-radius);
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transform: translateY(5px);
    transition: var(--admin-transition-fast);
    pointer-events: none;
    white-space: nowrap;
}

.modern-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--admin-gray-900);
}

.modern-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Cards */
.admin-card,
.stat-card {
    transition: var(--admin-transition);
    cursor: pointer;
}

.admin-card:hover,
.stat-card:hover {
    box-shadow: var(--admin-shadow-lg);
}

/* Modern Dropdown */
.dropdown-menu {
    border: none;
    box-shadow: var(--admin-shadow-xl);
    border-radius: var(--admin-radius-lg);
    padding: var(--admin-space-2);
    margin-top: var(--admin-space-2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dropdown-item {
    border-radius: var(--admin-radius);
    padding: var(--admin-space-3) var(--admin-space-4);
    font-weight: 500;
    transition: var(--admin-transition-fast);
}

.dropdown-item:hover {
    background: var(--admin-gold-gradient-soft);
    color: var(--admin-primary);
    transform: translateX(4px);
}

/* Page Transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: var(--admin-transition-slow);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--admin-transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    color: var(--admin-gray-600);
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid var(--admin-gray-200);
    border-top: 4px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--admin-space-4);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--admin-space-8) var(--admin-space-4);
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: var(--admin-gold-gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--admin-space-6);
    position: relative;
}

.empty-icon i {
    font-size: 3rem;
    color: var(--admin-primary);
}

.empty-title {
    font-family: var(--admin-font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--admin-gray-900);
    margin-bottom: var(--admin-space-2);
}

.empty-subtitle {
    color: var(--admin-gray-600);
    font-size: 1rem;
    margin-bottom: var(--admin-space-6);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Analytics Components */
.analytics-item {
    position: relative;
}

.analytics-label {
    font-size: 0.875rem;
    color: var(--admin-gray-600);
    font-weight: 500;
}

.analytics-value {
    font-size: 1.125rem;
    font-weight: 700;
}

.analytics-bar {
    height: 8px;
    background: var(--admin-gray-200);
    border-radius: 4px;
    margin-top: var(--admin-space-2);
    overflow: hidden;
}

.analytics-progress {
    height: 100%;
    background: var(--admin-gold-gradient);
    border-radius: 4px;
    transition: width 1s ease-in-out;
    position: relative;
}

.analytics-progress.success {
    background: linear-gradient(90deg, var(--admin-success), #10b981);
}

.analytics-progress.warning {
    background: linear-gradient(90deg, var(--admin-warning), #f59e0b);
}

.analytics-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Tables */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    border-radius: var(--admin-radius-lg);
    overflow: hidden;
}

.admin-table th,
.admin-table td {
    padding: var(--admin-space-4);
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.admin-table th {
    background: var(--admin-gold-gradient-soft);
    font-weight: 700;
    color: var(--admin-gray-900);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table tbody tr {
    transition: var(--admin-transition-fast);
}

.admin-table tbody tr:hover {
    background: rgba(207, 170, 19, 0.05);
    transform: scale(1.01);
}

/* Responsive Grid */
.g-3 {
    gap: var(--admin-space-4);
}

/* Modern Badges */
.badge {
    padding: var(--admin-space-1) var(--admin-space-3);
    border-radius: var(--admin-radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.text-primary { color: var(--admin-primary) !important; }
.text-success { color: var(--admin-success) !important; }
.text-warning { color: var(--admin-warning) !important; }
.text-info { color: var(--admin-info) !important; }

/* Modern Forms */
.form-group {
    margin-bottom: var(--admin-space-6);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--admin-space-2);
    font-weight: 600;
    color: var(--admin-gray-700);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    transition: var(--admin-transition-fast);
}

.form-control {
    width: 100%;
    padding: var(--admin-space-4) var(--admin-space-4);
    border: 2px solid var(--admin-gray-200);
    border-radius: var(--admin-radius-lg);
    font-size: 1rem;
    font-weight: 500;
    background: var(--admin-white);
    transition: var(--admin-transition);
    position: relative;
}

.form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 4px rgba(var(--admin-primary-rgb), 0.1);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    border-color: var(--admin-gray-300);
}

.form-control::placeholder {
    color: var(--admin-gray-400);
    font-weight: 400;
}

/* Floating Label Effect */
.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding: 1.625rem 1rem 0.625rem;
}

.form-floating .form-label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem;
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: var(--admin-transition);
    margin-bottom: 0;
    line-height: 1.25;
}

.form-floating .form-control:focus ~ .form-label,
.form-floating .form-control:not(:placeholder-shown) ~ .form-label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--admin-primary);
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--admin-success);
}

.badge-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--admin-warning);
}

.badge-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--admin-danger);
}

.badge-secondary {
    background: rgba(100, 116, 139, 0.1);
    color: var(--admin-secondary);
}

/* Modern Login Page */
.login-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.login-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(207, 170, 19, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(207, 170, 19, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

.login-wrapper::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
    animation: drift 20s linear infinite;
    opacity: 0.4;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

@keyframes drift {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-60px, -60px) rotate(360deg); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: var(--admin-space-12);
    border-radius: var(--admin-radius-2xl);
    box-shadow: var(--admin-shadow-xl), var(--admin-shadow-gold);
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--admin-gold-gradient);
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.login-header {
    text-align: center;
    margin-bottom: var(--admin-space-8);
    animation: fadeInDown 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.login-logo {
    width: 100px;
    height: 100px;
    background: var(--admin-gold-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--admin-space-6);
    box-shadow: var(--admin-shadow-gold);
    position: relative;
    overflow: hidden;
    animation: pulse 2s infinite;
}

.login-logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shine 3s infinite;
}

.login-logo img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    filter: brightness(0) invert(1);
    z-index: 1;
    position: relative;
}

.login-logo i {
    font-size: 2.5rem;
    color: white;
    z-index: 1;
    position: relative;
}

.login-title {
    font-family: var(--admin-font-display);
    font-size: 2.25rem;
    font-weight: 700;
    background: var(--admin-gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--admin-space-2);
    letter-spacing: -0.025em;
}

.login-subtitle {
    color: var(--admin-gray-600);
    font-size: 1rem;
    font-weight: 500;
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-content {
        padding: 1rem;
    }
}

/* Golden Theme Enhancements */
.admin-card:hover {
    box-shadow: 0 4px 12px rgba(207, 170, 19, 0.1);
    transition: all 0.3s ease;
}

.page-title {
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--admin-gold-gradient);
    border-radius: 2px;
}

.topbar-right .dropdown-toggle {
    color: var(--admin-primary);
    font-weight: 600;
}

.topbar-right .dropdown-toggle:hover {
    color: var(--admin-primary-dark);
}

/* Enhanced Button Styles */
.btn-admin-primary:hover {
    background: var(--admin-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(207, 170, 19, 0.3);
}

/* Enhanced Form Styles */
.form-label {
    color: var(--admin-primary);
    font-weight: 600;
}

/* Enhanced Badge Styles */
.badge-warning {
    background: var(--admin-gold-gradient);
    color: white;
}

/* Loading Animation */
.loading-spinner {
    border: 3px solid rgba(207, 170, 19, 0.3);
    border-top: 3px solid var(--admin-primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Golden Accent Elements */
.golden-accent {
    border-left: 4px solid var(--admin-primary);
    background: rgba(207, 170, 19, 0.05);
}

.golden-text {
    color: var(--admin-primary);
    font-weight: 600;
}

/* Enhanced Table Styling */
.admin-table thead th {
    background: linear-gradient(135deg, rgba(207, 170, 19, 0.1) 0%, rgba(207, 170, 19, 0.05) 100%);
    border-bottom: 2px solid var(--admin-primary);
}

/* Enhanced Alert Styling */
.alert-success {
    background: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
    border-left: 4px solid var(--admin-success);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.2);
    border-left: 4px solid var(--admin-danger);
}

.alert-warning {
    background: rgba(207, 170, 19, 0.1);
    border: 1px solid rgba(207, 170, 19, 0.2);
    border-left: 4px solid var(--admin-primary);
}

/* Profile Styles */
.profile-avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.profile-status-badge {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: #fff;
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.9rem;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.quick-action-item:hover {
    background: #f8f9fa;
    color: var(--admin-primary);
    text-decoration: none;
    transform: translateX(5px);
}

.quick-action-item i {
    width: 20px;
    margin-right: 0.75rem;
}

.profile-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    display: block;
}

.info-value {
    color: #6c757d;
    font-size: 0.95rem;
}

.badge-role {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.badge-super_admin {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.badge-admin {
    background: linear-gradient(135deg, var(--admin-primary), #b8941a);
    color: white;
}

.badge-editor {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.badge-status {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.badge-active {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.badge-inactive {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    width: 2px;
    height: 20px;
    background: #e9ecef;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    border: 2px solid #e9ecef;
}

.activity-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.gap-2 { gap: 1rem; }
