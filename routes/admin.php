<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\ProjectController;
use App\Http\Controllers\Admin\SliderController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\HomeContentController;
use App\Http\Controllers\Admin\HomePageSeoController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "admin" middleware group. Now create something great!
|
*/

// Redirect /admin to /admin/login
Route::get('/', function () {
    return redirect()->route('admin.login');
});

// Admin Authentication Routes (Guest only)
Route::middleware('guest:admin')->group(function () {
    Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');
});

// Admin Protected Routes
Route::middleware('auth:admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Logout
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');

    // Home Content Management
    Route::prefix('home-content')->name('home-content.')->group(function () {
        Route::get('/', [HomeContentController::class, 'index'])->name('index');
        Route::get('/{section}/manage', [HomeContentController::class, 'manage'])->name('manage');
        Route::get('/create', [HomeContentController::class, 'create'])->name('create');
        Route::post('/', [HomeContentController::class, 'store'])->name('store');
        Route::get('/{homeContent}/edit', [HomeContentController::class, 'edit'])->name('edit');
        Route::put('/{homeContent}', [HomeContentController::class, 'update'])->name('update');
        Route::delete('/{homeContent}', [HomeContentController::class, 'destroy'])->name('destroy');
        Route::post('/{homeContent}/toggle-status', [HomeContentController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Home Page SEO Management
    Route::prefix('home-seo')->name('home-seo.')->group(function () {
        Route::get('/', [HomePageSeoController::class, 'index'])->name('index');
        Route::put('/', [HomePageSeoController::class, 'update'])->name('update');
    });

    // Projects Management
    Route::prefix('projects')->name('projects.')->group(function () {
        Route::get('/', [ProjectController::class, 'index'])->name('index');
        Route::get('/create', [ProjectController::class, 'create'])->name('create');
        Route::post('/', [ProjectController::class, 'store'])->name('store');
        Route::get('/{project}', [ProjectController::class, 'show'])->name('show');
        Route::get('/{project}/edit', [ProjectController::class, 'edit'])->name('edit');
        Route::put('/{project}', [ProjectController::class, 'update'])->name('update');
        Route::delete('/{project}', [ProjectController::class, 'destroy'])->name('destroy');
    });

    // Sliders Management
    Route::prefix('sliders')->name('sliders.')->group(function () {
        Route::get('/', [SliderController::class, 'index'])->name('index');
        Route::get('/create', [SliderController::class, 'create'])->name('create');
        Route::post('/', [SliderController::class, 'store'])->name('store');
        Route::get('/{slider}', [SliderController::class, 'show'])->name('show');
        Route::get('/{slider}/edit', [SliderController::class, 'edit'])->name('edit');
        Route::put('/{slider}', [SliderController::class, 'update'])->name('update');
        Route::delete('/{slider}', [SliderController::class, 'destroy'])->name('destroy');
    });

    // Testimonials Management
    Route::prefix('testimonials')->name('testimonials.')->group(function () {
        Route::get('/', [TestimonialController::class, 'index'])->name('index');
        Route::get('/create', [TestimonialController::class, 'create'])->name('create');
        Route::post('/', [TestimonialController::class, 'store'])->name('store');
        Route::get('/{testimonial}', [TestimonialController::class, 'show'])->name('show');
        Route::get('/{testimonial}/edit', [TestimonialController::class, 'edit'])->name('edit');
        Route::put('/{testimonial}', [TestimonialController::class, 'update'])->name('update');
        Route::delete('/{testimonial}', [TestimonialController::class, 'destroy'])->name('destroy');
    });

    // Settings Management
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::put('/simple', [SettingsController::class, 'simpleUpdate'])->name('simple.update');
        Route::get('/category', [SettingsController::class, 'indexByCategory'])->name('category');
        Route::put('/', [SettingsController::class, 'update'])->name('update');
        Route::get('/create', [SettingsController::class, 'create'])->name('create');
        Route::post('/', [SettingsController::class, 'store'])->name('store');
        Route::get('/{setting}/edit', [SettingsController::class, 'edit'])->name('edit');
        Route::put('/{setting}', [SettingsController::class, 'updateSetting'])->name('update-setting');
        Route::delete('/{setting}', [SettingsController::class, 'destroy'])->name('destroy');
        Route::post('/clear-cache', [SettingsController::class, 'clearCache'])->name('clear-cache');
    });

    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('change-password');
        Route::put('/change-password', [ProfileController::class, 'updatePassword'])->name('update-password');
        Route::delete('/avatar', [ProfileController::class, 'removeAvatar'])->name('remove-avatar');
    });

    // Additional admin routes can be added here
    // Route::resource('users', UserController::class)->names('users');
    // Route::resource('orders', OrderController::class)->names('orders');
});
