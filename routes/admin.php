<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "admin" middleware group. Now create something great!
|
*/

// Redirect /admin to /admin/login
Route::get('/', function () {
    return redirect()->route('admin.login');
});

// Admin Authentication Routes (Guest only)
Route::middleware('guest:admin')->group(function () {
    Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');
});

// Admin Protected Routes
Route::middleware('auth:admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Logout
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');

    // Pages Management
    Route::resource('pages', \App\Http\Controllers\Admin\PageController::class);

    // Projects Management
    Route::resource('projects', \App\Http\Controllers\Admin\ProjectController::class);

    // Sliders Management
    Route::resource('sliders', \App\Http\Controllers\Admin\SliderController::class);

    // Testimonials Management
    Route::resource('testimonials', \App\Http\Controllers\Admin\TestimonialController::class);
});