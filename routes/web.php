<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminController;


Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/projects', [HomeController::class, 'projects'])->name('projects');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/about-us', [HomeController::class, 'aboutUs'])->name('about-us');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog');
Route::get('/media-center', [HomeController::class, 'mediaCenter'])->name('media-center');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/project/{slug}', [HomeController::class, 'projectDetails'])->name('project-details');
Route::get('/blog/{slug}', [HomeController::class, 'blogDetails'])->name('blog-details');


// Admin Authentication Routes (Guest only)
Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');

// Admin Protected Routes
Route::middleware('auth:admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Logout
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');



    // Additional admin routes can be added here
    // Route::resource('users', UserController::class)->names('users');
    // Route::resource('orders', OrderController::class)->names('orders');
});
