<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebsiteController;

// Dynamic Website Routes
Route::get('/', [WebsiteController::class, 'home'])->name('home');
Route::get('/about-us', [WebsiteController::class, 'about'])->name('about-us');
Route::get('/services', [WebsiteController::class, 'services'])->name('services');
Route::get('/contact', [WebsiteController::class, 'contact'])->name('contact');
Route::get('/blog', [WebsiteController::class, 'blog'])->name('blog');
Route::get('/media-center', [WebsiteController::class, 'mediaCenter'])->name('media-center');
Route::get('/projects', [WebsiteController::class, 'projects'])->name('projects');
Route::get('/project/{slug}', [WebsiteController::class, 'projectDetails'])->name('project-details');



