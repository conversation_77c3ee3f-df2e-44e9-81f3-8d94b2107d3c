<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HomeSliderController;
use App\Http\Controllers\Admin\HomeContentController;
use App\Http\Controllers\Admin\AboutUsController;
use App\Http\Controllers\Admin\ProjectsController;
use App\Http\Controllers\Admin\ServicesController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\BlogsController;
use App\Http\Controllers\Admin\SettingsController;


Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/projects', [HomeController::class, 'projects'])->name('projects');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/about-us', [HomeController::class, 'aboutUs'])->name('about-us');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog');
Route::get('/media-center', [HomeController::class, 'mediaCenter'])->name('media-center');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/project/{slug}', [HomeController::class, 'projectDetails'])->name('project-details');
Route::get('/blog/{slug}', [HomeController::class, 'blogDetails'])->name('blog-details');


// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');

    // Admin Protected Routes
    Route::middleware('auth')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

        // Admin Menu Routes
        Route::resource('home-slider', HomeSliderController::class);
        Route::resource('home-content', HomeContentController::class);
        Route::resource('about-us', AboutUsController::class);
        Route::resource('projects', ProjectsController::class);
        Route::resource('services', ServicesController::class);
        Route::resource('contact', ContactController::class);
        Route::resource('blogs', BlogsController::class);
        Route::resource('settings', SettingsController::class);
    });
});
