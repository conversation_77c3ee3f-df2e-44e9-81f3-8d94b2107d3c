<?php

if (!function_exists('setting')) {
    /**
     * Get setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        return app(\App\Services\SettingsService::class)->get($key, $default);
    }
}

if (!function_exists('logo_url')) {
    /**
     * Get logo URL with proper path handling
     *
     * @param string|null $logoPath
     * @param string $default
     * @return string
     */
    function logo_url($logoPath = null, $default = 'images/logos/Hestia Abodes Platinum Grey.png')
    {
        if (empty($logoPath)) {
            return asset($default);
        }

        // If path starts with 'images/', it's a public asset
        if (str_starts_with($logoPath, 'images/')) {
            return asset($logoPath);
        }

        // Otherwise, it's a storage file
        return asset('storage/' . $logoPath);
    }
}

if (!function_exists('settings')) {
    /**
     * Get settings service instance
     *
     * @return \App\Services\SettingsService
     */
    function settings()
    {
        return app(\App\Services\SettingsService::class);
    }
}
