<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Page;
use App\Models\Project;
use App\Models\Slider;
use App\Models\Testimonial;

class WebsiteController extends Controller
{
    public function home()
    {
        $page = Page::where('slug', 'home')->active()->first();
        $heroSliders = Slider::active()->ordered()->get();
        $projects = Project::active()->featured()->ordered()->limit(4)->get();
        $testimonials = Testimonial::active()->ordered()->get();

        return view('home', compact('page', 'heroSliders', 'projects', 'testimonials'));
    }

    public function about()
    {
        $page = Page::where('slug', 'about-us')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('about-us', compact('page'));
    }

    public function services()
    {
        $page = Page::where('slug', 'our-services')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('services', compact('page'));
    }

    public function contact()
    {
        $page = Page::where('slug', 'contact-us')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('contact', compact('page'));
    }

    public function blog()
    {
        $page = Page::where('slug', 'blog')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('blog', compact('page'));
    }

    public function mediaCenter()
    {
        $page = Page::where('slug', 'media-center')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('media-center', compact('page'));
    }

    public function projects()
    {
        $projects = Project::active()->ordered()->paginate(12);

        return view('projects', compact('projects'));
    }

    public function projectDetails($slug)
    {
        $project = Project::where('slug', $slug)->active()->firstOrFail();
        $relatedProjects = Project::where('id', '!=', $project->id)
                                 ->active()
                                 ->limit(3)
                                 ->get();

        return view('project-details', compact('project', 'relatedProjects'));
    }
}
