<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Slider;
use App\Models\HomeContent;

class HomeController extends Controller
{
    public function index()
    {
        // Get dynamic home content
        $homeContent = HomeContent::active()->ordered()->get()->keyBy('section');

        // Get SEO meta data
        $seoMeta = HomeContent::getSeoMeta();

        // Get hero slides from HomeContent
        $heroSliders = \App\Models\HomeContent::where('section', 'hero')
            ->where('item_type', 'slide')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function($slide) {
                // Add computed properties for backward compatibility
                $slide->image_url = $slide->image;
                $metaData = $slide->meta_data ? json_decode($slide->meta_data, true) : [];
                $metaData = $metaData ?? [];
                $slide->button_text_2 = $metaData['button_text_2'] ?? null;
                $slide->button_link_2 = $metaData['button_link_2'] ?? null;
                return $slide;
            });

        // Get projects marked for feature slider
        $featuredProjectSliders = \App\Models\Project::active()
            ->featureSlider()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get testimonials for testimonial slider
        $testimonialSliders = \App\Models\Testimonial::active()
            ->ordered()
            ->get();

        return view('home', compact('heroSliders', 'featuredProjectSliders', 'testimonialSliders', 'homeContent', 'seoMeta'));
    }

    public function projects()
    {
        // Fetch active projects from database
        $projects = \App\Models\Project::active()
            ->orderBy('featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get unique cities and project types for filters
        $cities = \App\Models\Project::active()
            ->distinct()
            ->pluck('city')
            ->filter();

        $projectTypes = \App\Models\Project::active()
            ->distinct()
            ->pluck('project_type')
            ->filter();

        return view('projects', compact('projects', 'cities', 'projectTypes'));
    }

    public function contact()
    {
        // Get settings for contact page
        $settings = \App\Models\Setting::getAllSettings();

        return view('contact', compact('settings'));
    }

    public function aboutUs()
    {
        // You can add why us page data fetching logic here
        // For example: team members, company stats, testimonials, etc.

        return view('about-us');
    }

    public function blog()
    {
        // You can add blog page data fetching logic here
        // For example: fetch blog posts from database, categories, featured posts, etc.

        return view('blog');
    }

    public function mediaCenter()
    {
        // You can add media center data fetching logic here
        // For example: fetch press releases, news articles, awards, events, etc.

        return view('media-center');
    }

    public function services()
    {
        // You can add services page data fetching logic here
        // For example: fetch service details, testimonials, pricing, etc.

        return view('services');
    }

    public function projectDetails($slug)
    {
        // Fetch project by slug from database
        $project = \App\Models\Project::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Get related projects (same city or project type, excluding current project)
        $relatedProjects = \App\Models\Project::where('is_active', true)
            ->where('id', '!=', $project->id)
            ->where(function($query) use ($project) {
                $query->where('city', $project->city)
                      ->orWhere('project_type', $project->project_type);
            })
            ->limit(3)
            ->get();

        return view('project-details', compact('project', 'relatedProjects'));
    }

    public function blogDetails($slug)
    {
        // You can add blog details data fetching logic here
        // For example: fetch blog post by slug from database, related posts, etc.

        // For now, we'll use static data
        $post = [
            'title' => 'Why Pune\'s Western Corridor Is Booming',
            'slug' => $slug,
            'author' => 'Akmal Raza',
            'date' => 'December 15, 2024'
        ];

        return view('blog-details', compact('post'));
    }
}
