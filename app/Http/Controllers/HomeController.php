<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Slider;
use App\Models\Page;

class HomeController extends Controller
{
    public function index()
    {
        // Get the home page with all its sections
        $page = Page::where('slug', 'home')->active()->first();

        if (!$page) {
            abort(404, 'Home page not found');
        }

        // Get all active sections ordered by sort_order
        $sections = $page->pageSections()
            ->active()
            ->ordered()
            ->get();

        // For backward compatibility, still get testimonials and projects separately
        // These will be used for fixed sections that are not dynamic
        $testimonialSliders = \App\Models\Testimonial::active()
            ->ordered()
            ->get();

        $featuredProjectSliders = \App\Models\Project::active()
            ->featureSlider()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('home', compact('page', 'sections', 'testimonialSliders', 'featuredProjectSliders'));
    }

    public function projects()
    {
        // Fetch active projects from database
        $projects = \App\Models\Project::active()
            ->orderBy('featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get unique cities and project types for filters
        $cities = \App\Models\Project::active()
            ->distinct()
            ->pluck('city')
            ->filter();

        $projectTypes = \App\Models\Project::active()
            ->distinct()
            ->pluck('project_type')
            ->filter();

        return view('projects', compact('projects', 'cities', 'projectTypes'));
    }

    public function contact()
    {
        $page = Page::where('slug', 'contact')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('contact', compact('page'));
    }

    public function aboutUs()
    {
        $page = Page::where('slug', 'about-us')->active()->first();

        if (!$page) {
            abort(404);
        }

        return view('about-us', compact('page'));
    }

    public function blog()
    {
        $page = Page::where('slug', 'blog')->active()->first();

        // If no dynamic page exists, use static content
        if (!$page) {
            return view('blog');
        }

        return view('blog', compact('page'));
    }

    public function mediaCenter()
    {
        $page = Page::where('slug', 'media-center')->active()->first();

        // If no dynamic page exists, use static content
        if (!$page) {
            return view('media-center');
        }

        return view('media-center', compact('page'));
    }

    public function services()
    {
        $page = Page::where('slug', 'services')->active()->first();

        // If no dynamic page exists, use static content
        if (!$page) {
            return view('services');
        }

        return view('services', compact('page'));
    }

    public function projectDetails($slug)
    {
        // Fetch project by slug from database
        $project = \App\Models\Project::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Get related projects (same city or project type, excluding current project)
        $relatedProjects = \App\Models\Project::where('is_active', true)
            ->where('id', '!=', $project->id)
            ->where(function($query) use ($project) {
                $query->where('city', $project->city)
                      ->orWhere('project_type', $project->project_type);
            })
            ->limit(3)
            ->get();

        return view('project-details', compact('project', 'relatedProjects'));
    }

    public function blogDetails($slug)
    {
        // You can add blog details data fetching logic here
        // For example: fetch blog post by slug from database, related posts, etc.

        // For now, we'll use static data
        $post = [
            'title' => 'Why Pune\'s Western Corridor Is Booming',
            'slug' => $slug,
            'author' => 'Akmal Raza',
            'date' => 'December 15, 2024'
        ];

        return view('blog-details', compact('post'));
    }
}
