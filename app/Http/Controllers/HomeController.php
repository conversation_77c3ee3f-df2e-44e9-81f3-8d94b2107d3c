<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // You can add data fetching logic here
        // For example: featured properties, testimonials, etc.

        return view('home');
    }

    public function projects()
    {
        // You can add projects data fetching logic here
        // For example: fetch projects from database, filter by categories, etc.

        return view('projects');
    }

    public function contact()
    {
        // You can add contact page data fetching logic here
        // For example: office locations, contact forms, etc.

        return view('contact');
    }

    public function aboutUs()
    {
        // You can add why us page data fetching logic here
        // For example: team members, company stats, testimonials, etc.

        return view('about-us');
    }

    public function blog()
    {
        // You can add blog page data fetching logic here
        // For example: fetch blog posts from database, categories, featured posts, etc.

        return view('blog');
    }

    public function mediaCenter()
    {
        // You can add media center data fetching logic here
        // For example: fetch press releases, news articles, awards, events, etc.

        return view('media-center');
    }

    public function services()
    {
        // You can add services page data fetching logic here
        // For example: fetch service details, testimonials, pricing, etc.

        return view('services');
    }

    public function projectDetails($slug)
    {
        // You can add project details data fetching logic here
        // For example: fetch project by slug from database, related projects, etc.

        // For now, we'll use static data
        $project = [
            'name' => 'Godrej Meridien',
            'slug' => $slug,
            'location' => 'Sector 106, Gurgaon',
            'price' => '₹1.62 Cr onwards'
        ];

        return view('project-details', compact('project'));
    }

    public function blogDetails($slug)
    {
        // You can add blog details data fetching logic here
        // For example: fetch blog post by slug from database, related posts, etc.

        // For now, we'll use static data
        $post = [
            'title' => 'Why Pune\'s Western Corridor Is Booming',
            'slug' => $slug,
            'author' => 'Akmal Raza',
            'date' => 'December 15, 2024'
        ];

        return view('blog-details', compact('post'));
    }
}
