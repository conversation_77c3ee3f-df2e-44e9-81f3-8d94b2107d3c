<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\PageSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PageSectionController extends Controller
{
    /**
     * Display sections for a specific page
     */
    public function index(Page $page)
    {
        $sections = $page->pageSections()->ordered()->get();
        $sectionTypes = $this->getSectionTypes();

        return view('admin.page-sections.index', compact('page', 'sections', 'sectionTypes'));
    }

    /**
     * Show form for creating a new section
     */
    public function create(Page $page)
    {
        $sectionTypes = $this->getSectionTypes();

        return view('admin.page-sections.create', compact('page', 'sectionTypes'));
    }

    /**
     * Store a new section
     */
    public function store(Request $request, Page $page)
    {
        $request->validate([
            'section_type' => 'required|string',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string',
            'content' => 'nullable|string',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'icon_type' => 'nullable|string|max:10',
            'button_text' => 'nullable|string|max:255',
            'button_url' => 'nullable|string|max:500',
            'cta_text' => 'nullable|string|max:255',
            'cta_url' => 'nullable|string|max:500',
            'secondary_cta_text' => 'nullable|string|max:255',
            'secondary_cta_url' => 'nullable|string|max:500',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'nullable|string|max:7',
            'show_stats' => 'boolean',
            'stats' => 'nullable|array',
            'stats.*.number' => 'nullable|string|max:50',
            'stats.*.label' => 'nullable|string|max:255',
            'items' => 'nullable|array',
            'items.*.title' => 'nullable|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.icon' => 'nullable|string|max:255',
            'items.*.image' => 'nullable|string|max:500',
            'items.*.url' => 'nullable|string|max:500',
            'items.*.category' => 'nullable|string|max:255',
            'items.*.designation' => 'nullable|string|max:255',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean'
        ]);

        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('page-sections/' . $page->slug, 'public');
                $images[] = $path;
            }
        }

        $section = $page->pageSections()->create([
            'section_type' => $request->section_type,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'content' => $request->content,
            'description' => $request->description,
            'icon' => $request->icon,
            'icon_type' => $request->icon_type ?? 'fas',
            'button_text' => $request->button_text,
            'button_url' => $request->button_url,
            'cta_text' => $request->cta_text,
            'cta_url' => $request->cta_url,
            'secondary_cta_text' => $request->secondary_cta_text,
            'secondary_cta_url' => $request->secondary_cta_url,
            'background_color' => $request->background_color,
            'text_color' => $request->text_color,
            'show_stats' => $request->boolean('show_stats', false),
            'stats' => $request->stats ? array_filter($request->stats, function($stat) {
                return !empty($stat['number']) || !empty($stat['label']);
            }) : null,
            'items' => $request->items ? array_filter($request->items, function($item) {
                return !empty($item['title']) || !empty($item['description']);
            }) : null,
            'images' => $images,
            'settings' => $request->settings ?? [],
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section created successfully!');
    }

    /**
     * Show form for editing a section
     */
    public function edit(Page $page, PageSection $section)
    {
        $sectionTypes = $this->getSectionTypes();

        return view('admin.page-sections.edit', compact('page', 'section', 'sectionTypes'));
    }

    /**
     * Update a section
     */
    public function update(Request $request, Page $page, PageSection $section)
    {
        $request->validate([
            'section_type' => 'required|string',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string',
            'content' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'icon_type' => 'nullable|string|max:10',
            'button_text' => 'nullable|string|max:255',
            'button_url' => 'nullable|string|max:500',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean'
        ]);

        $images = $section->images ?? [];

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('page-sections/' . $page->slug, 'public');
                $images[] = $path;
            }
        }

        // Handle image removals
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (($key = array_search($imageToRemove, $images)) !== false) {
                    unset($images[$key]);
                    Storage::disk('public')->delete($imageToRemove);
                }
            }
            $images = array_values($images); // Re-index array
        }

        $section->update([
            'section_type' => $request->section_type,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'content' => $request->content,
            'icon' => $request->icon,
            'icon_type' => $request->icon_type ?? 'fas',
            'button_text' => $request->button_text,
            'button_url' => $request->button_url,
            'images' => $images,
            'settings' => $request->settings ?? [],
            'sort_order' => $request->sort_order ?? $section->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section updated successfully!');
    }

    /**
     * Delete a section
     */
    public function destroy(Page $page, PageSection $section)
    {
        // Delete associated images
        if ($section->images) {
            foreach ($section->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $section->delete();

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section deleted successfully!');
    }

    /**
     * Update section order
     */
    public function updateOrder(Request $request, Page $page)
    {
        $request->validate([
            'sections' => 'required|array',
            'sections.*.id' => 'required|exists:page_sections,id',
            'sections.*.sort_order' => 'required|integer'
        ]);

        foreach ($request->sections as $sectionData) {
            PageSection::where('id', $sectionData['id'])
                      ->where('page_id', $page->id)
                      ->update(['sort_order' => $sectionData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Reorder sections
     */
    public function reorder(Request $request, Page $page)
    {
        $request->validate([
            'sections' => 'required|array',
            'sections.*.id' => 'required|integer|exists:page_sections,id',
            'sections.*.sort_order' => 'required|integer'
        ]);

        foreach ($request->sections as $sectionData) {
            PageSection::where('id', $sectionData['id'])
                ->where('page_id', $page->id)
                ->update(['sort_order' => $sectionData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Get available section types
     */
    private function getSectionTypes()
    {
        return [
            // Core sections
            'hero' => 'Hero Section',
            'about' => 'About/Who We Are Section',
            'mission_vision' => 'Mission & Vision Section',
            'services' => 'Services Section',
            'features' => 'Features/Why Choose Us Section',
            'testimonials' => 'Client Testimonials Section',
            'projects' => 'Featured Projects Section',
            'locations' => 'Project Locations Section',
            'stats' => 'Statistics Section',
            'team' => 'Team/Founder Section',
            'contact' => 'Contact Section',
            'cta' => 'Call to Action Section',

            // Project page specific
            'project_filters' => 'Project Filters Section',
            'project_grid' => 'Project Grid Section',

            // Additional sections
            'gallery' => 'Gallery Section',
            'faq' => 'FAQ Section',
            'blog' => 'Blog Section',
            'custom' => 'Custom Section'
        ];
    }
}
