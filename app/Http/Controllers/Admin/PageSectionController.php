<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\PageSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PageSectionController extends Controller
{
    /**
     * Display sections for a specific page
     */
    public function index(Page $page)
    {
        $sections = $page->pageSections()->ordered()->get();
        $sectionTypes = [
            'hero' => 'Hero Section',
            'about' => 'About Section',
            'services' => 'Services Section',
            'features' => 'Features Section',
            'testimonials' => 'Testimonials Section',
            'gallery' => 'Gallery Section',
            'contact' => 'Contact Section',
            'custom' => 'Custom Section'
        ];

        return view('admin.page-sections.index', compact('page', 'sections', 'sectionTypes'));
    }

    /**
     * Show form for creating a new section
     */
    public function create(Page $page)
    {
        $sectionTypes = [
            'hero' => 'Hero Section',
            'about' => 'About Section',
            'services' => 'Services Section',
            'features' => 'Features Section',
            'testimonials' => 'Testimonials Section',
            'gallery' => 'Gallery Section',
            'contact' => 'Contact Section',
            'custom' => 'Custom Section'
        ];

        return view('admin.page-sections.create', compact('page', 'sectionTypes'));
    }

    /**
     * Store a new section
     */
    public function store(Request $request, Page $page)
    {
        $request->validate([
            'section_type' => 'required|string',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string',
            'content' => 'nullable|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean'
        ]);

        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('page-sections/' . $page->slug, 'public');
                $images[] = $path;
            }
        }

        $section = $page->pageSections()->create([
            'section_type' => $request->section_type,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'content' => $request->content,
            'images' => $images,
            'settings' => $request->settings ?? [],
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section created successfully!');
    }

    /**
     * Show form for editing a section
     */
    public function edit(Page $page, PageSection $section)
    {
        $sectionTypes = [
            'hero' => 'Hero Section',
            'about' => 'About Section',
            'services' => 'Services Section',
            'features' => 'Features Section',
            'testimonials' => 'Testimonials Section',
            'gallery' => 'Gallery Section',
            'contact' => 'Contact Section',
            'custom' => 'Custom Section'
        ];

        return view('admin.page-sections.edit', compact('page', 'section', 'sectionTypes'));
    }

    /**
     * Update a section
     */
    public function update(Request $request, Page $page, PageSection $section)
    {
        $request->validate([
            'section_type' => 'required|string',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string',
            'content' => 'nullable|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean'
        ]);

        $images = $section->images ?? [];

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('page-sections/' . $page->slug, 'public');
                $images[] = $path;
            }
        }

        // Handle image removals
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (($key = array_search($imageToRemove, $images)) !== false) {
                    unset($images[$key]);
                    Storage::disk('public')->delete($imageToRemove);
                }
            }
            $images = array_values($images); // Re-index array
        }

        $section->update([
            'section_type' => $request->section_type,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'content' => $request->content,
            'images' => $images,
            'settings' => $request->settings ?? [],
            'sort_order' => $request->sort_order ?? $section->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section updated successfully!');
    }

    /**
     * Delete a section
     */
    public function destroy(Page $page, PageSection $section)
    {
        // Delete associated images
        if ($section->images) {
            foreach ($section->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $section->delete();

        return redirect()->route('admin.pages.sections.index', $page)
                        ->with('success', 'Section deleted successfully!');
    }

    /**
     * Update section order
     */
    public function updateOrder(Request $request, Page $page)
    {
        $request->validate([
            'sections' => 'required|array',
            'sections.*.id' => 'required|exists:page_sections,id',
            'sections.*.sort_order' => 'required|integer'
        ]);

        foreach ($request->sections as $sectionData) {
            PageSection::where('id', $sectionData['id'])
                      ->where('page_id', $page->id)
                      ->update(['sort_order' => $sectionData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }
}
