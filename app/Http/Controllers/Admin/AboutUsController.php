<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AboutUsSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AboutUsController extends Controller
{
    public function index()
    {
        $sections = AboutUsSection::ordered()->paginate(10);
        return view('admin.about-us.index', compact('sections'));
    }

    public function create()
    {
        return view('admin.about-us.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'required|string',
            'content_type' => 'required|in:text,icon,image',
            'icon_class' => 'nullable|string|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->except(['image']);
        
        // Generate section key from title if not provided
        if (empty($data['section_key'])) {
            $data['section_key'] = Str::slug($request->title);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('about-us', 'public');
            $data['image_path'] = $imagePath;
        }

        AboutUsSection::create($data);

        return redirect()->route('admin.about-us.index')
            ->with('success', 'About Us section created successfully.');
    }

    public function show(AboutUsSection $aboutUsSection)
    {
        return view('admin.about-us.show', compact('aboutUsSection'));
    }

    public function edit(AboutUsSection $aboutUsSection)
    {
        return view('admin.about-us.edit', compact('aboutUsSection'));
    }

    public function update(Request $request, AboutUsSection $aboutUsSection)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'required|string',
            'content_type' => 'required|in:text,icon,image',
            'icon_class' => 'nullable|string|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->except(['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($aboutUsSection->image_path) {
                Storage::disk('public')->delete($aboutUsSection->image_path);
            }
            
            $imagePath = $request->file('image')->store('about-us', 'public');
            $data['image_path'] = $imagePath;
        }

        $aboutUsSection->update($data);

        return redirect()->route('admin.about-us.index')
            ->with('success', 'About Us section updated successfully.');
    }

    public function destroy(AboutUsSection $aboutUsSection)
    {
        // Delete associated image if exists
        if ($aboutUsSection->image_path) {
            Storage::disk('public')->delete($aboutUsSection->image_path);
        }

        $aboutUsSection->delete();

        return redirect()->route('admin.about-us.index')
            ->with('success', 'About Us section deleted successfully.');
    }
}
