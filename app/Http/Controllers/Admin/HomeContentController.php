<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use App\Models\Project;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class HomeContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $sections = HomeContent::getAllSections();

        // Get main content items with their children
        $contents = HomeContent::mainItems()
            ->with('activeChildren')
            ->orderBy('sort_order')
            ->get()
            ->keyBy('section');

        $projects = Project::active()->get(); // For project selection

        // Get SEO settings
        $seoSettings = [
            'home_meta_title' => Setting::getValue('home_meta_title', 'Hestia Abodes - Premium Real Estate Solutions in Pune & Mumbai'),
            'home_meta_description' => Setting::getValue('home_meta_description', 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.'),
            'home_meta_keywords' => Setting::getValue('home_meta_keywords', 'real estate, property, investment, residential, commercial, Pune, Mumbai, Hestia Abodes'),
            'home_og_title' => Setting::getValue('home_og_title', 'Hestia Abodes - Premium Real Estate Solutions'),
            'home_og_description' => Setting::getValue('home_og_description', 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.'),
            'home_og_image' => Setting::getValue('home_og_image', ''),
        ];

        return view('admin.home-content.index', compact('sections', 'contents', 'projects', 'seoSettings'));
    }

    /**
     * Show the form for managing a specific section.
     */
    public function manage($section)
    {
        $sections = HomeContent::getAllSections();

        if (!array_key_exists($section, $sections)) {
            abort(404, 'Section not found');
        }

        // Get main content for this section with children
        $mainContent = HomeContent::where('section', $section)
            ->whereNull('parent_id')
            ->with('children')
            ->first();

        $projects = Project::active()->get();

        return view('admin.home-content.manage', compact('section', 'sections', 'mainContent', 'projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $sections = HomeContent::getAllSections();
        $projects = Project::active()->get();
        $section = $request->get('section');
        $parentId = $request->get('parent_id');

        return view('admin.home-content.create', compact('sections', 'projects', 'section', 'parentId'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HomeContent $homeContent)
    {
        $sections = HomeContent::getAllSections();
        $projects = Project::active()->get();

        return view('admin.home-content.edit', compact('homeContent', 'sections', 'projects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'parent_id' => 'nullable|exists:home_contents,id',
            'section' => 'required|string',
            'item_type' => 'nullable|string|in:main,sub_item,feature',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['image', 'og_image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('home-content', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $data['og_image'] = $request->file('og_image')->store('home-content/og', 'public');
        }

        HomeContent::create($data);

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section created successfully.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HomeContent $homeContent)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['image', 'og_image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($homeContent->image) {
                Storage::disk('public')->delete($homeContent->image);
            }
            $data['image'] = $request->file('image')->store('home-content', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            // Delete old OG image
            if ($homeContent->og_image) {
                Storage::disk('public')->delete($homeContent->og_image);
            }
            $data['og_image'] = $request->file('og_image')->store('home-content/og', 'public');
        }

        $homeContent->update($data);

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HomeContent $homeContent)
    {
        // Delete associated images
        if ($homeContent->image) {
            Storage::disk('public')->delete($homeContent->image);
        }
        if ($homeContent->og_image) {
            Storage::disk('public')->delete($homeContent->og_image);
        }

        $homeContent->delete();

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section deleted successfully.');
    }

    /**
     * Toggle active status
     */
    public function toggleStatus(HomeContent $homeContent)
    {
        $homeContent->update(['is_active' => !$homeContent->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $homeContent->is_active,
            'message' => 'Status updated successfully.'
        ]);
    }

    /**
     * Update SEO settings
     */
    public function updateSeo(Request $request)
    {
        $request->validate([
            'home_meta_title' => 'required|string|max:255',
            'home_meta_description' => 'required|string|max:500',
            'home_meta_keywords' => 'required|string|max:255',
            'home_og_title' => 'required|string|max:255',
            'home_og_description' => 'required|string|max:500',
            'home_og_image' => 'nullable|string|max:255',
        ]);

        // Update all SEO settings
        Setting::setValue('home_meta_title', $request->home_meta_title);
        Setting::setValue('home_meta_description', $request->home_meta_description);
        Setting::setValue('home_meta_keywords', $request->home_meta_keywords);
        Setting::setValue('home_og_title', $request->home_og_title);
        Setting::setValue('home_og_description', $request->home_og_description);
        Setting::setValue('home_og_image', $request->home_og_image);

        return response()->json([
            'success' => true,
            'message' => 'SEO settings updated successfully!'
        ]);
    }
}
