<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomeContent;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class HomeContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $sections = HomeContent::getAllSections();

        // Get main content items with their children
        $contents = HomeContent::mainItems()
            ->with('activeChildren')
            ->orderBy('sort_order')
            ->get()
            ->keyBy('section');

        $projects = Project::active()->get(); // For project selection

        return view('admin.home-content.index', compact('sections', 'contents', 'projects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'parent_id' => 'nullable|exists:home_contents,id',
            'section' => 'required|string',
            'item_type' => 'nullable|string|in:main,sub_item,feature',
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['image', 'og_image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('home-content', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $data['og_image'] = $request->file('og_image')->store('home-content/og', 'public');
        }

        HomeContent::create($data);

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section created successfully.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HomeContent $homeContent)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['image', 'og_image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($homeContent->image) {
                Storage::disk('public')->delete($homeContent->image);
            }
            $data['image'] = $request->file('image')->store('home-content', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            // Delete old OG image
            if ($homeContent->og_image) {
                Storage::disk('public')->delete($homeContent->og_image);
            }
            $data['og_image'] = $request->file('og_image')->store('home-content/og', 'public');
        }

        $homeContent->update($data);

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HomeContent $homeContent)
    {
        // Delete associated images
        if ($homeContent->image) {
            Storage::disk('public')->delete($homeContent->image);
        }
        if ($homeContent->og_image) {
            Storage::disk('public')->delete($homeContent->og_image);
        }

        $homeContent->delete();

        return redirect()->route('admin.home-content.index')
            ->with('success', 'Home content section deleted successfully.');
    }

    /**
     * Toggle active status
     */
    public function toggleStatus(HomeContent $homeContent)
    {
        $homeContent->update(['is_active' => !$homeContent->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $homeContent->is_active,
            'message' => 'Status updated successfully.'
        ]);
    }
}
