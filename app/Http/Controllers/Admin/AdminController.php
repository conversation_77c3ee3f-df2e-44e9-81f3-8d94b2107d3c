<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Slider;
use App\Models\Testimonial;
use App\Models\Setting;

class AdminController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        // Get dashboard statistics
        $stats = [
            'projects' => Project::count(),
            'active_projects' => Project::where('is_active', true)->count(),
            'sliders' => Slider::count(),
            'testimonials' => Testimonial::count(),
        ];

        // Get recent projects
        $recentProjects = Project::latest()->limit(5)->get();

        // Get recent testimonials
        $recentTestimonials = Testimonial::latest()->limit(5)->get();

        return view('admin.dashboard', compact('stats', 'recentProjects', 'recentTestimonials'));
    }
}
