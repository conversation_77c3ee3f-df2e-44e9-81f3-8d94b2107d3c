<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use App\Models\Page;

class ImageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $imagesByPage = [];
        $pagesPath = public_path('images/pages');

        if (File::exists($pagesPath)) {
            $pageFolders = File::directories($pagesPath);

            foreach ($pageFolders as $folder) {
                $pageName = basename($folder);
                $images = [];

                $imageFiles = File::files($folder);
                foreach ($imageFiles as $file) {
                    if (in_array(strtolower($file->getExtension()), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        $images[] = [
                            'name' => $file->getFilename(),
                            'path' => 'images/pages/' . $pageName . '/' . $file->getFilename(),
                            'size' => $file->getSize(),
                            'modified' => $file->getMTime()
                        ];
                    }
                }

                if (!empty($images)) {
                    $imagesByPage[$pageName] = $images;
                }
            }
        }

        // Get pages from database for reference
        $pages = Page::all();

        return view('admin.images.index', compact('imagesByPage', 'pages'));
    }

    /**
     * Upload new images for a specific page
     */
    public function upload(Request $request)
    {
        $request->validate([
            'page' => 'required|string',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048'
        ]);

        $page = $request->input('page');
        $uploadPath = public_path('images/pages/' . $page);

        if (!File::exists($uploadPath)) {
            File::makeDirectory($uploadPath, 0755, true);
        }

        $uploadedFiles = [];

        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $file) {
                $filename = time() . '_' . $file->getClientOriginalName();
                $file->move($uploadPath, $filename);
                $uploadedFiles[] = $filename;
            }
        }

        return redirect()->route('admin.images.index')
                        ->with('success', 'Images uploaded successfully: ' . implode(', ', $uploadedFiles));
    }

    /**
     * Delete an image
     */
    public function delete(Request $request)
    {
        $request->validate([
            'image_path' => 'required|string'
        ]);

        $imagePath = public_path($request->input('image_path'));

        if (File::exists($imagePath)) {
            File::delete($imagePath);
            return redirect()->route('admin.images.index')
                            ->with('success', 'Image deleted successfully.');
        }

        return redirect()->route('admin.images.index')
                        ->with('error', 'Image not found.');
    }

    /**
     * Replace an image
     */
    public function replace(Request $request)
    {
        $request->validate([
            'old_image_path' => 'required|string',
            'new_image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048'
        ]);

        $oldImagePath = public_path($request->input('old_image_path'));

        if (File::exists($oldImagePath)) {
            // Delete old image
            File::delete($oldImagePath);

            // Upload new image with same name
            $newImage = $request->file('new_image');
            $filename = basename($request->input('old_image_path'));
            $directory = dirname($oldImagePath);

            $newImage->move($directory, $filename);

            return redirect()->route('admin.images.index')
                            ->with('success', 'Image replaced successfully.');
        }

        return redirect()->route('admin.images.index')
                        ->with('error', 'Original image not found.');
    }
}
