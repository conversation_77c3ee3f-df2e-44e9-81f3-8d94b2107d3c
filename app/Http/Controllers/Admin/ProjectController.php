<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $projects = Project::latest()->get();
        return view('admin.projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.projects.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:projects,slug',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'location' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'developer' => 'required|string|max:255',
            'project_type' => 'required|string|max:255',
            'property_types' => 'required|string|max:255',
            'starting_price' => 'required|numeric|min:0',
            'price_range' => 'required|string|max:255',
            'possession_date' => 'required|string|max:255',
            'rera_id' => 'nullable|string|max:255',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'nullable|integer|min:0',
            'status' => 'required|in:upcoming,ongoing,ready_to_move,completed',
            'featured' => 'boolean',
            'feature_slider' => 'boolean',
            'sort_order' => 'nullable|integer',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'floor_plans.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,pdf|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // Handle multiple image uploads
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('projects', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        // Handle floor plan uploads
        if ($request->hasFile('floor_plans')) {
            $floorPlans = [];
            foreach ($request->file('floor_plans') as $plan) {
                $path = $plan->store('floor-plans', 'public');
                $floorPlans[] = $path;
            }
            $data['floor_plans'] = $floorPlans;
        }

        // Handle amenities and specifications as arrays
        if ($request->has('amenities')) {
            $data['amenities'] = array_filter($request->input('amenities'));
        }

        if ($request->has('specifications')) {
            $data['specifications'] = array_filter($request->input('specifications'));
        }

        Project::create($data);

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        return view('admin.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        return view('admin.projects.edit', compact('project'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:projects,slug,' . $project->id,
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'location' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'developer' => 'required|string|max:255',
            'project_type' => 'required|string|max:255',
            'property_types' => 'required|string|max:255',
            'starting_price' => 'required|numeric|min:0',
            'price_range' => 'required|string|max:255',
            'possession_date' => 'required|string|max:255',
            'rera_id' => 'nullable|string|max:255',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'nullable|integer|min:0',
            'status' => 'required|in:upcoming,ongoing,ready_to_move,completed',
            'featured' => 'boolean',
            'feature_slider' => 'boolean',
            'sort_order' => 'nullable|integer',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'floor_plans.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,pdf|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // Handle multiple image uploads
        if ($request->hasFile('images')) {
            // Delete old images
            if ($project->images) {
                foreach ($project->images as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('projects', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        // Handle floor plan uploads
        if ($request->hasFile('floor_plans')) {
            // Delete old floor plans
            if ($project->floor_plans) {
                foreach ($project->floor_plans as $oldPlan) {
                    Storage::disk('public')->delete($oldPlan);
                }
            }

            $floorPlans = [];
            foreach ($request->file('floor_plans') as $plan) {
                $path = $plan->store('floor-plans', 'public');
                $floorPlans[] = $path;
            }
            $data['floor_plans'] = $floorPlans;
        }

        // Handle amenities and specifications as arrays
        if ($request->has('amenities')) {
            $data['amenities'] = array_filter($request->input('amenities'));
        }

        if ($request->has('specifications')) {
            $data['specifications'] = array_filter($request->input('specifications'));
        }

        $project->update($data);

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        // Delete associated images
        if ($project->images) {
            foreach ($project->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        // Delete associated floor plans
        if ($project->floor_plans) {
            foreach ($project->floor_plans as $plan) {
                Storage::disk('public')->delete($plan);
            }
        }

        $project->delete();

        return redirect()->route('admin.projects.index')
            ->with('success', 'Project deleted successfully.');
    }
}
