<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pages = Page::ordered()->get();
        return view('admin.pages.index', compact('pages'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.pages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'template' => 'required|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer',
            'hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only([
            'name', 'title', 'slug', 'template', 'content',
            'meta_description', 'meta_keywords', 'sort_order'
        ]);

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $data['is_active'] = $request->has('is_active');

        // Handle file uploads
        $heroImagePath = null;
        if ($request->hasFile('hero_image')) {
            $heroImagePath = $request->file('hero_image')->store('pages/hero', 'public');
        }

        // Handle gallery images upload
        $galleryImages = [];
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $image) {
                $path = $image->store('pages/gallery', 'public');
                $galleryImages[] = $path;
            }
        }

        $data['images'] = $galleryImages;

        // Build sections from form data
        $sections = $this->buildSectionsFromFormForCreate($request, $heroImagePath);
        $data['sections'] = $sections;

        Page::create($data);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully!');
    }

    private function buildSectionsFromFormForCreate(Request $request, $heroImagePath = null)
    {
        $sections = [];

        // Hero Section
        if ($request->filled(['hero_title']) || $heroImagePath) {
            $heroSection = [
                'type' => 'hero',
                'key' => 'hero_section',
                'title' => $request->input('hero_title'),
                'subtitle' => $request->input('hero_subtitle'),
                'description' => $request->input('hero_description'),
                'button_text' => $request->input('hero_button_text'),
                'button_link' => $request->input('hero_button_link')
            ];

            if ($heroImagePath) {
                $heroSection['image'] = $heroImagePath;
            }

            $sections[] = $heroSection;
        }

        // Contact Information Section
        if ($request->filled(['contact_phone']) || $request->filled(['contact_email'])) {
            $sections[] = [
                'type' => 'contact_info',
                'key' => 'contact_details',
                'title' => 'Contact Information',
                'phone' => $request->input('contact_phone'),
                'email' => $request->input('contact_email'),
                'address' => $request->input('contact_address'),
                'working_hours' => $request->input('contact_hours')
            ];
        }

        // Services Section
        if ($request->filled(['services_title'])) {
            $sections[] = [
                'type' => 'services_grid',
                'key' => 'services_list',
                'title' => $request->input('services_title'),
                'description' => $request->input('services_description')
            ];
        }

        // Statistics Section (for About page)
        if ($request->has('stats')) {
            $stats = [];
            foreach ($request->input('stats') as $stat) {
                if (!empty($stat['number']) && !empty($stat['label'])) {
                    $stats[] = $stat;
                }
            }

            if (!empty($stats)) {
                $sections[] = [
                    'type' => 'stats',
                    'key' => 'company_stats',
                    'title' => 'Our Achievements',
                    'stats' => $stats
                ];
            }
        }

        // Handle sections data from the dynamic form (if any)
        if ($request->has('sections')) {
            foreach ($request->input('sections') as $section) {
                // Parse JSON data if provided
                if (!empty($section['data'])) {
                    $jsonData = json_decode($section['data'], true);
                    if ($jsonData !== null) {
                        $section = array_merge($section, $jsonData);
                        unset($section['data']);
                    }
                }
                $sections[] = $section;
            }
        }

        return $sections;
    }

    /**
     * Display the specified resource.
     */
    public function show(Page $page)
    {
        return view('admin.pages.show', compact('page'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page)
    {
        return view('admin.pages.edit', compact('page'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Page $page)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $page->id,
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'template' => 'required|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer',
            'hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only([
            'name', 'title', 'slug', 'template', 'content',
            'meta_description', 'meta_keywords', 'sort_order'
        ]);

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $data['is_active'] = $request->has('is_active');

        // Handle file uploads
        $existingImages = $page->images ?? [];

        // Handle hero image upload
        $heroImagePath = null;
        if ($request->hasFile('hero_image')) {
            $heroImagePath = $request->file('hero_image')->store('pages/hero', 'public');
        }

        // Handle gallery images upload
        $galleryImages = [];
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $image) {
                $path = $image->store('pages/gallery', 'public');
                $galleryImages[] = $path;
            }
        }

        // Remove selected images
        if ($request->has('remove_images')) {
            $removeImages = $request->input('remove_images');
            $existingImages = array_diff($existingImages, $removeImages);

            // Delete files from storage
            foreach ($removeImages as $imagePath) {
                \Storage::disk('public')->delete($imagePath);
            }
        }

        // Merge existing and new gallery images
        $data['images'] = array_merge($existingImages, $galleryImages);

        // Build sections from form data
        $sections = $this->buildSectionsFromForm($request, $page, $heroImagePath);
        $data['sections'] = $sections;

        $page->update($data);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully!');
    }

    private function buildSectionsFromForm(Request $request, Page $page, $heroImagePath = null)
    {
        $sections = [];
        $existingSections = $page->sections ?? [];

        // Hero Section
        if ($request->filled(['hero_title']) || $heroImagePath) {
            $heroSection = [
                'type' => 'hero',
                'key' => 'hero_section',
                'title' => $request->input('hero_title'),
                'subtitle' => $request->input('hero_subtitle'),
                'description' => $request->input('hero_description'),
                'button_text' => $request->input('hero_button_text'),
                'button_link' => $request->input('hero_button_link')
            ];

            // Use new image or keep existing
            if ($heroImagePath) {
                $heroSection['image'] = $heroImagePath;
            } else {
                $existingHero = collect($existingSections)->firstWhere('type', 'hero');
                if ($existingHero && isset($existingHero['image'])) {
                    $heroSection['image'] = $existingHero['image'];
                }
            }

            $sections[] = $heroSection;
        }

        // Contact Information Section
        if ($request->filled(['contact_phone']) || $request->filled(['contact_email'])) {
            $sections[] = [
                'type' => 'contact_info',
                'key' => 'contact_details',
                'title' => 'Contact Information',
                'phone' => $request->input('contact_phone'),
                'email' => $request->input('contact_email'),
                'address' => $request->input('contact_address'),
                'working_hours' => $request->input('contact_hours')
            ];
        }

        // Services Section
        if ($request->filled(['services_title'])) {
            $sections[] = [
                'type' => 'services_grid',
                'key' => 'services_list',
                'title' => $request->input('services_title'),
                'description' => $request->input('services_description')
            ];
        }

        // Statistics Section (for About page)
        if ($request->has('stats')) {
            $stats = [];
            foreach ($request->input('stats') as $stat) {
                if (!empty($stat['number']) && !empty($stat['label'])) {
                    $stats[] = $stat;
                }
            }

            if (!empty($stats)) {
                $sections[] = [
                    'type' => 'stats',
                    'key' => 'company_stats',
                    'title' => 'Our Achievements',
                    'stats' => $stats
                ];
            }
        }

        // Preserve other existing sections that weren't handled by the form
        foreach ($existingSections as $section) {
            $type = $section['type'] ?? '';
            if (!in_array($type, ['hero', 'contact_info', 'services_grid', 'stats'])) {
                $sections[] = $section;
            }
        }

        return $sections;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page)
    {
        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }
}
