<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Display simple settings form
     */
    public function index(Request $request)
    {
        $settings = Setting::getAllSettings();
        return view('admin.settings.simple', compact('settings'));
    }

    /**
     * Display settings by category (old method)
     */
    public function indexByCategory(Request $request)
    {
        $category = $request->get('category', 'general');
        $categories = Setting::getCategories();

        // Validate category
        if (!array_key_exists($category, $categories)) {
            $category = 'general';
        }

        $settings = Setting::active()
            ->byCategory($category)
            ->ordered()
            ->get();

        return view('admin.settings.index', compact('settings', 'categories', 'category'));
    }

    /**
     * Update simple settings
     */
    public function simpleUpdate(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'company_name' => 'nullable|string|max:255',
            'company_tagline' => 'nullable|string|max:255',
            'company_description' => 'nullable|string|max:500',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'contact_whatsapp' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:500',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,svg|max:2048',
            'favicon' => 'nullable|image|mimes:ico,png|max:1024',
            'primary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'accent_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'text_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'footer_text' => 'nullable|string|max:255',
        ]);

        try {
            // Handle file uploads
            $uploadedFiles = [];

            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                $oldLogo = Setting::where('key', 'logo')->first();
                if ($oldLogo && $oldLogo->value && Storage::disk('public')->exists($oldLogo->value)) {
                    Storage::disk('public')->delete($oldLogo->value);
                }
                $uploadedFiles['logo'] = $request->file('logo')->store('logos', 'public');
            }

            if ($request->hasFile('favicon')) {
                // Delete old favicon if exists
                $oldFavicon = Setting::where('key', 'favicon')->first();
                if ($oldFavicon && $oldFavicon->value && Storage::disk('public')->exists($oldFavicon->value)) {
                    Storage::disk('public')->delete($oldFavicon->value);
                }
                $uploadedFiles['favicon'] = $request->file('favicon')->store('favicons', 'public');
            }

            // Update all settings
            $settingsData = $request->except(['_token', '_method', 'logo', 'favicon']);
            $settingsData = array_merge($settingsData, $uploadedFiles);

            foreach ($settingsData as $key => $value) {
                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'is_active' => true,
                        'category' => $this->getCategoryForKey($key)
                    ]
                );
            }

            // Clear all cache to ensure settings reflect immediately
            Setting::clearCache();
            $this->settingsService->clearCache();

            // Clear cache
            $this->settingsService->clearCache();

            return redirect()->route('admin.settings.index')
                ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating settings: ' . $e->getMessage());
        }
    }

    /**
     * Get category for setting key
     */
    private function getCategoryForKey($key)
    {
        $categoryMap = [
            'site_name' => 'general',
            'site_tagline' => 'general',
            'site_description' => 'general',
            'company_name' => 'company',
            'company_tagline' => 'company',
            'company_description' => 'company',
            'contact_phone' => 'contact',
            'contact_email' => 'contact',
            'contact_whatsapp' => 'contact',
            'contact_address' => 'contact',
            'logo' => 'appearance',
            'favicon' => 'appearance',
            'primary_color' => 'appearance',
            'secondary_color' => 'appearance',
            'accent_color' => 'appearance',
            'text_color' => 'appearance',
            'footer_text' => 'appearance',
        ];

        return $categoryMap[$key] ?? 'general';
    }

    /**
     * Update settings (old method)
     */
    public function update(Request $request)
    {
        $category = $request->get('category', 'general');
        $settings = Setting::active()->byCategory($category)->get();

        // Build validation rules
        $rules = [];
        foreach ($settings as $setting) {
            $rule = [];
            
            if ($setting->is_required) {
                $rule[] = 'required';
            } else {
                $rule[] = 'nullable';
            }

            switch ($setting->type) {
                case 'email':
                    $rule[] = 'email';
                    break;
                case 'url':
                    $rule[] = 'url';
                    break;
                case 'number':
                    $rule[] = 'numeric';
                    break;
                case 'file':
                    $rule[] = 'file';
                    $rule[] = 'mimes:jpeg,png,jpg,gif,pdf,doc,docx';
                    $rule[] = 'max:2048';
                    break;
                case 'boolean':
                    $rule[] = 'boolean';
                    break;
            }

            if (!empty($rule)) {
                $rules[$setting->key] = implode('|', $rule);
            }
        }

        $validated = $request->validate($rules);

        // Process and save settings
        foreach ($settings as $setting) {
            $value = $validated[$setting->key] ?? null;

            // Handle file uploads
            if ($setting->type === 'file' && $request->hasFile($setting->key)) {
                // Delete old file if exists
                if ($setting->value && Storage::disk('public')->exists($setting->value)) {
                    Storage::disk('public')->delete($setting->value);
                }

                $file = $request->file($setting->key);
                $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('settings', $filename, 'public');
                $value = $path;
            }

            // Handle boolean values
            if ($setting->type === 'boolean') {
                $value = $request->has($setting->key) ? 1 : 0;
            }

            // Update setting
            $setting->update(['value' => $value]);
        }

        // Clear cache
        $this->settingsService->clearCache();

        return redirect()
            ->route('admin.settings.index', ['category' => $category])
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Show form to create new setting
     */
    public function create()
    {
        $categories = Setting::getCategories();
        $types = Setting::getTypes();

        return view('admin.settings.create', compact('categories', 'types'));
    }

    /**
     * Store new setting
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|unique:settings,key|max:255',
            'label' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', array_keys(Setting::getTypes())),
            'category' => 'required|string|in:' . implode(',', array_keys(Setting::getCategories())),
            'description' => 'nullable|string|max:1000',
            'options' => 'nullable|string',
            'is_required' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Process options for select type
        if ($validated['type'] === 'select' && !empty($validated['options'])) {
            $options = [];
            $lines = explode("\n", $validated['options']);
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    if (strpos($line, '=') !== false) {
                        [$key, $value] = explode('=', $line, 2);
                        $options[trim($key)] = trim($value);
                    } else {
                        $options[$line] = $line;
                    }
                }
            }
            $validated['options'] = $options;
        } else {
            $validated['options'] = null;
        }

        // Set default sort order
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = Setting::byCategory($validated['category'])->max('sort_order') + 1;
        }

        Setting::create($validated);

        return redirect()
            ->route('admin.settings.index', ['category' => $validated['category']])
            ->with('success', 'Setting created successfully.');
    }

    /**
     * Show form to edit setting
     */
    public function edit(Setting $setting)
    {
        $categories = Setting::getCategories();
        $types = Setting::getTypes();

        // Convert options array to string for editing
        $optionsString = '';
        if ($setting->options && is_array($setting->options)) {
            foreach ($setting->options as $key => $value) {
                $optionsString .= "{$key}={$value}\n";
            }
        }

        return view('admin.settings.edit', compact('setting', 'categories', 'types', 'optionsString'));
    }

    /**
     * Update specific setting
     */
    public function updateSetting(Request $request, Setting $setting)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:settings,key,' . $setting->id,
            'label' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', array_keys(Setting::getTypes())),
            'category' => 'required|string|in:' . implode(',', array_keys(Setting::getCategories())),
            'description' => 'nullable|string|max:1000',
            'options' => 'nullable|string',
            'is_required' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Process options for select type
        if ($validated['type'] === 'select' && !empty($validated['options'])) {
            $options = [];
            $lines = explode("\n", $validated['options']);
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    if (strpos($line, '=') !== false) {
                        [$key, $value] = explode('=', $line, 2);
                        $options[trim($key)] = trim($value);
                    } else {
                        $options[$line] = $line;
                    }
                }
            }
            $validated['options'] = $options;
        } else {
            $validated['options'] = null;
        }

        $setting->update($validated);

        return redirect()
            ->route('admin.settings.index', ['category' => $validated['category']])
            ->with('success', 'Setting updated successfully.');
    }

    /**
     * Delete setting
     */
    public function destroy(Setting $setting)
    {
        // Delete associated file if exists
        if ($setting->type === 'file' && $setting->value && Storage::disk('public')->exists($setting->value)) {
            Storage::disk('public')->delete($setting->value);
        }

        $category = $setting->category;
        $setting->delete();

        return redirect()
            ->route('admin.settings.index', ['category' => $category])
            ->with('success', 'Setting deleted successfully.');
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        $this->settingsService->clearCache();

        return redirect()
            ->back()
            ->with('success', 'Settings cache cleared successfully.');
    }
}
