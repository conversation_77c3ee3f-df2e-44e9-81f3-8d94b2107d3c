<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;

class HomePageSeoController extends Controller
{
    public function index()
    {
        $seoSettings = [
            'home_meta_title' => Setting::getValue('home_meta_title', 'Hestia Abodes - Premium Real Estate Solutions in Pune & Mumbai'),
            'home_meta_description' => Setting::getValue('home_meta_description', 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.'),
            'home_meta_keywords' => Setting::getValue('home_meta_keywords', 'real estate, property, investment, residential, commercial, Pune, Mumbai, Hestia Abodes'),
            'home_og_title' => Setting::getValue('home_og_title', 'Hestia Abodes - Premium Real Estate Solutions'),
            'home_og_description' => Setting::getValue('home_og_description', 'Whether you\'re a home buyer searching for your ideal property or a builder looking to optimize sales through exclusive mandates, Hestia Abodes ensures a transparent, strategic, and reliable real estate experience.'),
            'home_og_image' => Setting::getValue('home_og_image', ''),
        ];

        return view('admin.home-seo.index', compact('seoSettings'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'home_meta_title' => 'required|string|max:255',
            'home_meta_description' => 'required|string|max:500',
            'home_meta_keywords' => 'nullable|string|max:255',
            'home_og_title' => 'required|string|max:255',
            'home_og_description' => 'required|string|max:500',
            'home_og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle OG image upload
        if ($request->hasFile('home_og_image')) {
            // Delete old image if exists
            $oldImage = Setting::getValue('home_og_image');
            if ($oldImage && Storage::disk('public')->exists($oldImage)) {
                Storage::disk('public')->delete($oldImage);
            }

            // Store new image
            $imagePath = $request->file('home_og_image')->store('seo', 'public');
            Setting::setValue('home_og_image', $imagePath);
        }

        // Update other SEO settings
        Setting::setValue('home_meta_title', $request->home_meta_title);
        Setting::setValue('home_meta_description', $request->home_meta_description);
        Setting::setValue('home_meta_keywords', $request->home_meta_keywords);
        Setting::setValue('home_og_title', $request->home_og_title);
        Setting::setValue('home_og_description', $request->home_og_description);

        return redirect()->route('admin.home-seo.index')
            ->with('success', 'Home page SEO settings updated successfully!');
    }
}
