<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Setting;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(\App\Services\SettingsService::class, function ($app) {
            return new \App\Services\SettingsService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share settings with all views (excluding admin routes to prevent conflicts)
        View::composer(['layouts.app', 'partials.*', 'contact', 'home', 'about', 'services', 'projects'], function ($view) {
            try {
                $settings = Setting::getAllSettings();
                $view->with('settings', $settings);
            } catch (\Exception $e) {
                // Handle case where settings table doesn't exist yet
                $view->with('settings', []);
            }
        });
    }
}
