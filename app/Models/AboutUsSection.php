<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class AboutUsSection extends Model
{
    protected $fillable = [
        'section_key',
        'title',
        'subtitle',
        'description',
        'content_type',
        'icon_class',
        'image_path',
        'image_position',
        'columns_count',
        'subcontent',
        'sort_order',
        'is_active',
        'additional_data'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'additional_data' => 'array',
        'subcontent' => 'array'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        return $this->image_path ? asset('storage/' . $this->image_path) : null;
    }

    // Helper methods
    public function hasIcon()
    {
        return $this->content_type === 'icon' && !empty($this->icon_class);
    }

    public function hasImage()
    {
        return $this->content_type === 'image' && !empty($this->image_path);
    }
}
