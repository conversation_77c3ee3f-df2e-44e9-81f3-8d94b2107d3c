<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class HomeContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_id',
        'section',
        'item_type',
        'title',
        'subtitle',
        'description',
        'image',
        'icon',
        'button_text',
        'button_link',
        'meta_data',
        'is_active',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
    ];

    protected $casts = [
        'meta_data' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function parent()
    {
        return $this->belongsTo(HomeContent::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(HomeContent::class, 'parent_id');
    }

    public function activeChildren()
    {
        return $this->hasMany(HomeContent::class, 'parent_id')->where('is_active', true)->orderBy('sort_order');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    public function scopeBySection($query, $section)
    {
        return $query->where('section', $section);
    }

    public function scopeMainItems($query)
    {
        return $query->whereNull('parent_id')->where('item_type', 'main');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if (!$this->image) {
            return asset('images/default-hero.jpg');
        }

        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        return Storage::url($this->image);
    }

    public function getOgImageUrlAttribute()
    {
        if (!$this->og_image) {
            return $this->image_url;
        }

        if (filter_var($this->og_image, FILTER_VALIDATE_URL)) {
            return $this->og_image;
        }

        return Storage::url($this->og_image);
    }

    // Static methods
    public static function getBySection($section)
    {
        return static::bySection($section)->active()->first();
    }

    public static function getAllSections()
    {
        return [
            'hero' => 'Hero Section',
            'about' => 'Who We Are Section',
            'featured_projects' => 'Featured Projects',
            'why_choose_us' => 'Why Choose Hestia Abodes',
            'services' => 'Our Services',
            'portfolio' => 'Our Portfolio',
            'testimonials' => 'What Our Clients Say',
            'contact_form' => 'Contact Form Section',
        ];
    }

    public static function getSeoMeta()
    {
        $heroSection = static::getBySection('hero');

        return [
            'title' => $heroSection?->meta_title ?? 'Hestia Abodes - Premium Real Estate Solutions',
            'description' => $heroSection?->meta_description ?? 'Discover premium residential and commercial properties with Hestia Abodes. Your trusted partner in real estate investment and property management.',
            'keywords' => $heroSection?->meta_keywords ?? 'real estate, property, investment, residential, commercial, Pune, Mumbai',
            'og_title' => $heroSection?->og_title ?? $heroSection?->meta_title ?? 'Hestia Abodes - Premium Real Estate Solutions',
            'og_description' => $heroSection?->og_description ?? $heroSection?->meta_description ?? 'Discover premium residential and commercial properties with Hestia Abodes.',
            'og_image' => $heroSection?->og_image_url ?? asset('images/og-default.jpg'),
        ];
    }
}
