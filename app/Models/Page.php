<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Page extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'title',
        'meta_description',
        'meta_keywords',
        'content',
        'sections',
        'images',
        'template',
        'is_active',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'sections' => 'array',
            'images' => 'array',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ];
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Scope for active pages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered pages
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Get page URL
     */
    public function getUrlAttribute(): string
    {
        return url($this->slug);
    }

    /**
     * Get sections by type
     */
    public function getSectionsByType($type)
    {
        if (!$this->sections) {
            return collect();
        }

        return collect($this->sections)->where('type', $type);
    }

    /**
     * Get section by key
     */
    public function getSection($key)
    {
        if (!$this->sections) {
            return null;
        }

        return collect($this->sections)->firstWhere('key', $key);
    }

    /**
     * Get the page sections relationship
     */
    public function pageSections()
    {
        return $this->hasMany(PageSection::class);
    }

    /**
     * Get active page sections ordered by sort_order
     */
    public function activePageSections()
    {
        return $this->pageSections()->active()->ordered();
    }
}
