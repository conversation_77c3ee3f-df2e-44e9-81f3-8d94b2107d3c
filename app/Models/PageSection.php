<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PageSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_id',
        'section_type',
        'title',
        'subtitle',
        'content',
        'description',
        'icon',
        'icon_type',
        'button_text',
        'button_url',
        'images',
        'settings',
        'sort_order',
        'is_active',
        'background_color',
        'text_color',
        'show_stats',
        'stats',
        'items',
        'cta_text',
        'cta_url',
        'secondary_cta_text',
        'secondary_cta_url'
    ];

    protected $casts = [
        'images' => 'array',
        'settings' => 'array',
        'stats' => 'array',
        'items' => 'array',
        'is_active' => 'boolean',
        'show_stats' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Get the page that owns the section
     */
    public function page()
    {
        return $this->belongsTo(Page::class);
    }

    /**
     * Scope for active sections
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered sections
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc');
    }

    /**
     * Scope by section type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('section_type', $type);
    }
}
